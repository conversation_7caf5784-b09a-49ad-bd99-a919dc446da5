<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="script-src 'self'">
    <title data-i18n="dashboard_page_title">Dashboard - Système de Gestion</title>

    <!-- Pré-chargement des traductions pour éviter le flash -->
    <script>
        // Pré-chargement immédiat de la langue depuis localStorage
        (function() {
            const savedLang = localStorage.getItem('app-language') || 'fr';
            document.documentElement.lang = savedLang;
            document.documentElement.dir = savedLang === 'ar' ? 'rtl' : 'ltr';
            if (savedLang === 'ar') {
                document.documentElement.classList.add('rtl');
            }
        })();
    </script>

    <link href="./css/output.css" rel="stylesheet">
    <style>
        /* Améliorations pour le mode sombre */
        .dark {
            color-scheme: dark;
        }

        /* Amélioration du contraste pour le menu */
        .dark .nav-link {
            color: #e5e7eb !important;
        }

        .dark .nav-link:hover {
            color: white !important;
        }

        /* Amélioration des cartes en mode sombre */
        .dark .bg-white {
            background-color: #1f2937 !important;
            border-color: #374151 !important;
        }

        /* Amélioration des bordures */
        .dark .border-gray-200 {
            border-color: #374151 !important;
        }

        .dark .border-gray-300 {
            border-color: #4b5563 !important;
        }

        /* Amélioration du texte */
        .dark .text-gray-600 {
            color: #d1d5db !important;
        }

        .dark .text-gray-700 {
            color: #e5e7eb !important;
        }

        .dark .text-gray-800 {
            color: #f3f4f6 !important;
        }

        .dark .text-gray-900 {
            color: #f9fafb !important;
        }

        /* Amélioration des inputs */
        .dark input, .dark textarea, .dark select {
            background-color: #374151 !important;
            border-color: #4b5563 !important;
            color: #f3f4f6 !important;
        }

        .dark input:focus, .dark textarea:focus, .dark select:focus {
            border-color: #6366f1 !important;
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1) !important;
        }

        /* Amélioration des boutons */
        .dark .bg-gray-100 {
            background-color: #374151 !important;
        }

        .dark .bg-gray-200 {
            background-color: #4b5563 !important;
        }

        /* Amélioration des tableaux */
        .dark table {
            background-color: #1f2937 !important;
        }

        .dark th {
            background-color: #374151 !important;
            color: #f3f4f6 !important;
        }

        .dark td {
            border-color: #4b5563 !important;
            color: #e5e7eb !important;
        }

        /* Amélioration des modales */
        .dark .modal-content {
            background-color: #1f2937 !important;
            border-color: #374151 !important;
        }

        /* Amélioration des notifications */
        .dark .notification {
            background-color: #1f2937 !important;
            border-color: #374151 !important;
            color: #f3f4f6 !important;
        }

        /* Amélioration du sidebar */
        .dark #sidebar {
            background-color: #111827 !important;
            border-color: #374151 !important;
        }

        /* Amélioration des icônes */
        .dark .text-gray-400 {
            color: #9ca3af !important;
        }

        .dark .text-gray-500 {
            color: #6b7280 !important;
        }

        /* Amélioration des ombres */
        .dark .shadow-md {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2) !important;
        }

        .dark .shadow-lg {
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2) !important;
        }

        /* Amélioration des hover states */
        .dark .hover\:bg-gray-100:hover {
            background-color: #374151 !important;
        }

        .dark .hover\:bg-gray-200:hover {
            background-color: #4b5563 !important;
        }

        /* Amélioration des focus states */
        .dark .focus\:ring-blue-500:focus {
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3) !important;
        }

        /* Amélioration des alertes */
        .dark .bg-red-50 {
            background-color: #7f1d1d !important;
        }

        .dark .bg-green-50 {
            background-color: #14532d !important;
        }

        .dark .bg-yellow-50 {
            background-color: #78350f !important;
        }

        .dark .bg-blue-50 {
            background-color: #1e3a8a !important;
        }

        /* Amélioration des badges */
        .dark .badge {
            background-color: #374151 !important;
            color: #f3f4f6 !important;
        }

        /* Amélioration des dividers */
        .dark hr {
            border-color: #374151 !important;
        }

        /* Amélioration des placeholders */
        .dark ::placeholder {
            color: #9ca3af !important;
        }

        /* Amélioration des scrollbars */
        .dark ::-webkit-scrollbar {
            background-color: #1f2937;
        }

        .dark ::-webkit-scrollbar-thumb {
            background-color: #4b5563;
            border-radius: 6px;
        }

        .dark ::-webkit-scrollbar-thumb:hover {
            background-color: #6b7280;
        }

        /* Amélioration des transitions */
        * {
            transition-property: background-color, border-color, color, fill, stroke, opacity, box-shadow, transform;
            transition-duration: 200ms;
            transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
        }
    </style>
</head>
<body class="bg-gray-100 dark:bg-gray-900 flex h-screen">
    <aside class="w-64 bg-gray-800 text-white flex flex-col flex-shrink-0">
        <div class="p-4 text-2xl font-bold border-b border-gray-700">GestionPro</div>
        <nav id="main-nav" class="flex-grow"></nav>
    </aside>

    <main class="flex-1 p-8 overflow-y-auto">
        <h1 class="text-3xl font-bold text-gray-800 dark:text-white mb-6" data-i18n="dashboard_main_title">Tableau de Bord</h1>

        <div class="mb-6 bg-white dark:bg-gray-800 p-4 rounded-lg shadow-md flex items-center justify-between">
            <div class="flex items-center gap-2">
                <button data-period="today" class="period-btn px-3 py-1 text-sm rounded-md" data-i18n="today">Aujourd'hui</button>
                <button data-period="week" class="period-btn px-3 py-1 text-sm rounded-md" data-i18n="this_week">Cette Semaine</button>
                <button data-period="month" class="period-btn px-3 py-1 text-sm rounded-md" data-i18n="this_month">Ce Mois-ci</button>
            </div>
            <div class="flex items-center gap-2 text-sm">
                <span data-i18n="from">De:</span>
                <input type="date" id="startDate" class="p-1 border rounded dark:bg-gray-700 dark:border-gray-600">
                <span data-i18n="to">à:</span>
                <input type="date" id="endDate" class="p-1 border rounded dark:bg-gray-700 dark:border-gray-600">
                <button id="filterByDate" class="px-3 py-1 text-sm rounded-md bg-blue-600 text-white" data-i18n="filter_button">Filtrer</button>
            </div>
        </div>

        <!-- Statistiques Principales -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
                <h2 class="text-lg font-semibold text-gray-500 dark:text-gray-400" data-i18n="turnover_sales">Chiffre d'Affaires (Ventes)</h2>
                <p class="text-4xl font-bold mt-2 text-green-600">
                    <span id="revenue-stat">0.00</span> MAD
                </p>
            </div>

            <div id="profit-stat-card" class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md hidden">
                <h2 class="text-lg font-semibold text-gray-500 dark:text-gray-400" data-i18n="net_profit">Bénéfice Net</h2>
                <p class="text-4xl font-bold mt-2 text-blue-500">
                    <span id="profit-stat">0.00</span> MAD
                </p>
            </div>

            <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
                <h2 class="text-lg font-semibold text-gray-500 dark:text-gray-400" data-i18n="credits_granted">Crédits Accordés</h2>
                <p class="text-4xl font-bold mt-2 text-orange-500">
                    <span id="credit-stat">0.00</span> MAD
                </p>
            </div>
        </div>

        <!-- Section Analytics Produits -->
        <div class="mb-8">
            <div class="flex items-center justify-between mb-6">
                <h2 class="text-2xl font-bold text-gray-800 dark:text-white flex items-center">
                    <span class="mr-3">📊</span>
                    <span data-i18n="product_analytics">Analytics Produits</span>
                </h2>
                <button id="exportAnalytics" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    <span data-i18n="export_data">Exporter</span>
                </button>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Top Bénéfices Card -->
                <div class="bg-gradient-to-br from-green-50 to-emerald-100 dark:from-green-900/20 dark:to-emerald-900/20 p-6 rounded-xl shadow-lg border border-green-200 dark:border-green-800">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-bold text-green-800 dark:text-green-200 flex items-center">
                            <span class="mr-2">💰</span>
                            <span data-i18n="top_profitable_products">Produits les Plus Rentables</span>
                        </h3>
                        <div class="animate-pulse">
                            <div id="profitable-loading" class="w-4 h-4 bg-green-400 rounded-full hidden"></div>
                        </div>
                    </div>
                    <div id="profitable-products" class="space-y-3">
                        <!-- Contenu dynamique -->
                    </div>
                </div>

                <!-- Top Ventes Card -->
                <div class="bg-gradient-to-br from-blue-50 to-purple-100 dark:from-blue-900/20 dark:to-purple-900/20 p-6 rounded-xl shadow-lg border border-blue-200 dark:border-blue-800">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-bold text-blue-800 dark:text-blue-200 flex items-center">
                            <span class="mr-2">🔥</span>
                            <span data-i18n="top_selling_products">Produits les Plus Vendus</span>
                        </h3>
                        <div class="animate-pulse">
                            <div id="selling-loading" class="w-4 h-4 bg-blue-400 rounded-full hidden"></div>
                        </div>
                    </div>
                    <div id="selling-products" class="space-y-3">
                        <!-- Contenu dynamique -->
                    </div>
                </div>

                <!-- Performance Globale Card -->
                <div class="bg-gradient-to-br from-purple-50 to-pink-100 dark:from-purple-900/20 dark:to-pink-900/20 p-6 rounded-xl shadow-lg border border-purple-200 dark:border-purple-800">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-bold text-purple-800 dark:text-purple-200 flex items-center">
                            <span class="mr-2">📊</span>
                            <span data-i18n="performance_overview">Vue d'Ensemble</span>
                        </h3>
                        <button id="viewPerformanceDetails" class="text-sm text-purple-600 hover:text-purple-800 dark:text-purple-400 dark:hover:text-purple-200 transition-colors">
                            <span data-i18n="view_details">Voir Détails</span>
                        </button>
                    </div>
                    <div id="performance-overview" class="space-y-3">
                        <!-- Contenu dynamique -->
                    </div>
                </div>

                <!-- Insights Rapides Card -->
                <div class="bg-gradient-to-br from-yellow-50 to-orange-100 dark:from-yellow-900/20 dark:to-orange-900/20 p-6 rounded-xl shadow-lg border border-yellow-200 dark:border-yellow-800">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-bold text-yellow-800 dark:text-yellow-200 flex items-center">
                            <span class="mr-2">🎯</span>
                            <span data-i18n="quick_insights">Recommandations</span>
                        </h3>
                    </div>
                    <div id="quick-insights" class="space-y-3">
                        <!-- Contenu dynamique -->
                    </div>
                </div>
            </div>
        </div>
    </main>
    
    <script src="./js/i18n.js"></script>
    <script src="./js/preloader.js"></script>
    <script src="./js/page-initializer.js"></script>
    <script src="./js/notifications.js"></script>
    <script src="./js/layout.js"></script>
    <script src="./js/dashboard.js"></script>
</body>
</html>