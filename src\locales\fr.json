{"main_menu_dashboard": "Dashboard", "main_menu_cash_register": "Caisse", "main_menu_products": "Produits & Stock", "main_menu_price_adjustment": "Ajustement des Prix", "main_menu_stock_adjustment": "Ajustement de Stock", "main_menu_clients": "Clients", "main_menu_history": "Historique", "main_menu_credits": "Gestion Crédits", "main_menu_invoices": "Facturation", "main_menu_settings": "Paramètres", "main_menu_seller_history": "<PERSON><PERSON>", "add": "Ajouter", "edit": "Modifier", "delete": "<PERSON><PERSON><PERSON><PERSON>", "save": "Enregistrer", "cancel": "Annuler", "search": "Rechercher...", "product_name": "Nom du produit", "product_category": "<PERSON><PERSON><PERSON><PERSON>", "add_product_button": "Ajouter un Produit", "search_by_name_or_barcode": "Rechercher par nom ou code-barres...", "product_name_header": "Nom", "barcode_header": "Code-barres", "retail_price_header": "Prix Détail", "wholesale_price_header": "Prix Gros", "stock_header": "Stock", "actions_header": "Actions", "no_product_found": "Aucun produit trouvé.", "add_product_modal_title": "Ajouter un Produit", "edit_product_modal_title": "Modifier le Produit", "choose_image_button": "Choisir une image...", "product_name_label": "Nom du produit", "barcode_label": "Code-barres", "category_label": "<PERSON><PERSON><PERSON><PERSON>", "purchase_price_label": "Prix d'Achat", "retail_price_label": "Prix Détail", "wholesale_price_label": "Prix Gros", "carton_price_label": "Prix Carton", "pieces_per_carton_label": "Pièces par Carton", "stock_label": "Stock", "alert_threshold_label": "<PERSON><PERSON> d'alerte", "save_button": "<PERSON><PERSON><PERSON><PERSON>", "cancel_button": "Annuler", "confirm_button": "✓ Confirmer", "confirm_delete_product": "Êtes-vous sûr de vouloir supprimer ce produit ? Cette action est irréversible.", "delete_failed": "La suppression a échoué", "error_loading_products": "Erreur lors du chargement des produits", "error_saving_product": "Erreur lors de la sauvegarde du produit", "error_barcode_unique": "Erreur : Ce code-barres est déjà utilisé par un autre produit.", "operation_failed": "L'opération a échoué", "error_name_price_required": "Le nom et le prix de détail sont obligatoires.", "clients_page_title": "Gestion des Clients", "add_client_button": "Ajouter un Client", "search_by_name_phone_ice": "Rechercher par nom, téléphone, ICE...", "name_header": "Nom", "phone_header": "Téléphone", "ice_header": "ICE", "address_header": "<PERSON><PERSON><PERSON>", "credit_header": "Crédit", "no_client_found": "Aucun client trouvé.", "add_client_modal_title": "Ajouter un Client", "edit_client_modal_title": "Modifier le Client", "client_name_label": "Nom du client", "client_phone_label": "Téléphone", "client_ice_label": "ICE", "client_address_label": "<PERSON><PERSON><PERSON>", "confirm_delete_client": "Êtes-vous sûr de vouloir supprimer ce client ? Toutes ses données de crédit et son historique seront conservés, mais le client sera archivé.", "cash_register_page_title": "Caisse", "search_product_placeholder": "Rechercher un produit par nom ou code-barres...", "all_categories": "Toutes", "cart_title": "<PERSON><PERSON>", "product_header": "Produit", "quantity_header": "Qté", "unit_price_header": "Prix U.", "total_header": "Total", "cart_is_empty": "Le panier est vide. Cliquez sur un produit pour l'ajouter.", "default_client": "Client de passage", "search_client_placeholder": "Commencez à taper...", "total_amount": "Montant Total :", "amount_paid": "<PERSON><PERSON>", "credit": "Crédit", "change_due": "Rendu", "set_total_amount_button": "Montant Total", "cancel_sale_button": "Annuler la Vente", "cash_payment_button": "💰 Paiement Comptant", "check_payment_button": "📄 Paiement Chèque", "credit_payment_button": "💳 Crédit / Partiel", "validate_payment_button": "Valider le Paiement", "choose_payment_type": "Choisissez le type de paiement :", "partial_payment_details": "Détails du paiement partiel :", "credit_amount": "Crédit :", "back_button": "Retour", "save_correction_button": "Enregistrer la Correction", "continue_to_payment_button": "→ Continuer vers Paiement", "edit_payment_step_info": "Choisissez la méthode de paiement pour cette vente", "payment_method_header": "Méthode de Paiement", "client_up_to_date": "À jour", "filter_all": "Tous", "filter_in_stock": "En stock", "filter_alert": "<PERSON><PERSON><PERSON>", "filter_out_of_stock": "Rupture", "total_products": "Total", "in_stock_products": "En stock", "alert_products": "<PERSON><PERSON><PERSON>", "out_of_stock_products": "Rupture", "changed_products": "Modifiés", "average_margin": "<PERSON>ge moy.", "reset_changes": "Réinitialiser", "apply_bulk_margin": "Appliquer <PERSON>", "apply_bulk_adjustment": "Appliquer Ajustement", "bulk_margin_placeholder": "Marge %", "bulk_adjustment_placeholder": "<PERSON><PERSON>", "margin_based_on": "Marge basée sur", "margin_retail": "Prix Détail", "margin_wholesale": "Prix Gros", "margin_carton": "Prix Carton", "stock_filter_all": "Tous", "stock_filter_in_stock": "En stock", "stock_filter_alert": "<PERSON><PERSON><PERSON>", "stock_filter_out_of_stock": "Ruptures", "stock_total_value": "Valeur totale", "stock_changed_products": "Modifiés", "bulk_adjustment_add": "Ajouter", "bulk_adjustment_subtract": "<PERSON><PERSON><PERSON>", "bulk_adjustment_set": "Définir", "bulk_threshold_apply": "<PERSON><PERSON><PERSON><PERSON>", "stock_movement": "Mouvement", "stock_value": "<PERSON><PERSON>", "stock_threshold": "<PERSON><PERSON>", "credits_dashboard": "Tableau de bord crédits", "total_clients": "Total clients", "debtor_clients": "Clients débiteurs", "total_credit_amount": "Crédit total", "monthly_collection": "Encaissé ce mois", "overdue_clients": "Clients en retard", "recovery_rate": "Taux de recouvrement", "credit_risk_low": "Risque faible", "credit_risk_medium": "<PERSON><PERSON><PERSON> moyen", "credit_risk_high": "Risque élevé", "credit_risk_critical": "Risque critique", "age_recent": "<PERSON><PERSON><PERSON>", "age_medium": "<PERSON><PERSON><PERSON>", "age_old": "Ancien", "quick_payment": "Encaissement rapide", "payment_history": "Historique des paiements", "credit_adjustment": "Ajustement de crédit", "add_credit": "A<PERSON>ter crédit", "subtract_credit": "<PERSON><PERSON><PERSON> crédit", "payment_note": "Note de paiement", "transaction_history": "Historique des transactions", "sale_details": "<PERSON><PERSON><PERSON> de la vente", "products_sold": "Produits vendus", "sale_number": "<PERSON><PERSON><PERSON><PERSON> vente", "payment_method": "Paiement", "credit_sale": "Vente à crédit", "unit_price": "Prix U.", "quantity_short": "Qté", "click_for_details": "Cliquer pour détails", "no_product_details": "Aucun détail de produit disponible", "loading_history": "Chargement de l'historique...", "no_transactions_found": "Aucune transaction trouvée pour ce client", "history_load_error": "Erreur lors du chargement de l'historique", "last_sale_panel_title": "<PERSON><PERSON><PERSON>", "edit_sale_button": "Modifier la vente", "time_left_to_edit": "Temps restant pour modifier", "expired": "expiré", "quick_add_client_button": "Ajout Rapide Client", "stock_max_reached": "Stock maximum atteint pour ce produit.", "cart_is_empty_alert": "Le panier est vide.", "credit_for_default_client_error": "Impossible de faire un crédit au 'Client de passage'. Veuillez sélectionner un client enregistré.", "sale_processed_success": "Vente #%s enregistrée avec succès !", "sale_corrected_success": "Vente #%s corrigée avec succès !", "sale_failed_unknown": "La vente a échoué pour une raison inconnue.", "validation_error": "Erreur de validation", "edit_sale_unsupported": "La modification de vente n'est pas encore compatible avec la vente par carton.", "edit_details_error": "Impossible de retrouver les détails de la vente.", "edit_mode_error": "Une erreur est survenue lors de la préparation de la modification.", "search_product_label": "Rechercher un produit", "categories_label": "Catégories", "client_label": "Client", "barcode_scanner_label": "Scanner Code-Barres", "barcode_scanner_placeholder": "<PERSON><PERSON><PERSON> ou tapez le code-barres...", "scanner_ready": "<PERSON><PERSON><PERSON><PERSON>", "scanner_scanning": "Scan en cours...", "product_found_by_barcode": "Produit trouvé et ajouté au panier", "product_not_found_by_barcode": "Aucun produit trouvé avec ce code-barres", "barcode_scan_error": "<PERSON><PERSON><PERSON> lors du <PERSON>", "invalid_barcode": "Code-barres invalide", "barcode_already_exists": "Ce code-barres existe déjà", "barcode_valid": "Code-barres valide", "print_ticket_button": "<PERSON><PERSON><PERSON><PERSON> Ticket", "print_options_title": "Options d'Impression", "print_options_description": "Choisissez comment vous souhaitez imprimer le ticket :", "export_pdf_title": "Exporter en PDF", "export_pdf_description": "Télécharger le ticket au format PDF", "direct_print_title": "Impression Directe", "direct_print_description": "Imprimer directement sur l'imprimante thermique", "ticket_number": "Ticket N°", "date_time": "Date", "seller": "<PERSON><PERSON><PERSON>", "customer": "Client", "quantity": "Qté", "subtotal": "Sous-total", "discount": "Remise", "amount_received": "<PERSON><PERSON><PERSON>", "change_given": "Monnaie", "advance_payment": "Avance", "remaining_credit": "<PERSON><PERSON><PERSON> restant", "thank_you_message": "Merci pour votre visite !", "return_policy": "Retours sous 7 jours", "company_name": "MAGASIN GÉNÉRAL", "company_address": "123 Avenue Mohammed V, Casablanca", "company_phone": "Tél: +*********** 456", "company_ice": "ICE: 001234567890123", "cash": "Espèces", "check": "Chèque", "dashboard_page_title": "Tableau de Bord - Système de Gestion", "dashboard_main_title": "Tableau <PERSON>", "today": "<PERSON><PERSON><PERSON>'hui", "this_week": "<PERSON><PERSON>", "this_month": "Ce <PERSON>-ci", "from": "De:", "to": "à:", "filter_button": "<PERSON><PERSON><PERSON>", "turnover_sales": "<PERSON><PERSON><PERSON>'<PERSON> (Ventes)", "net_profit": "Bénéfice Net", "credits_granted": "Crédits Accordés", "price_adjustment_page_title": "Ajustement des Prix", "save_changes_button": "Enregistrer les Modifications", "search_product_placeholder_simple": "Rechercher un produit...", "product_header_simple": "Produit", "purchase_price_header": "Prix d'Achat", "carton_price_header": "Prix Carton", "no_changes_to_save": "Aucune modification de prix à enregistrer.", "products_updated_success": "%s produit(s) mis à jour avec succès !", "error_saving_changes": "Une erreur est survenue lors de la sauvegarde.", "owner_only_access": "Accès réservé au Propriétaire.", "stock_adjustment_page_title": "Ajustement de Stock", "stock_adjustment_main_title": "Ajustement de Stock (Inventaire)", "save_adjustments_button": "Sauvegarder les Ajustements", "adjustment_reason_placeholder": "<PERSON><PERSON><PERSON>'ajustement (ex: Inventaire annuel)", "current_stock_header": "Stock Actuel", "new_stock_header": "Nouveau Stock", "reason_required_alert": "Veuillez fournir un motif pour l'ajustement (ex: Inventaire annuel).", "no_stock_changes_alert": "Aucun changement de stock n'a été saisi.", "confirm_stock_adjustment": "Confirmez-vous %s ajustement(s) de stock ?", "stock_updated_success": "Les stocks ont été mis à jour avec succès !", "stock_update_failed": "Erreur lors de la mise à jour des stocks.", "history_page_title": "Historique des Ventes", "filter_by_client": "Filtrer par Client", "type_client_name": "Taper le nom du client...", "filter_by_product": "Filtrer par Produit", "type_product_name": "Taper le nom du produit...", "from_date": "<PERSON>", "to_date": "Au", "reset_button": "Réinitialiser", "date_header": "Date", "sale_id_header": "N° Vente", "line_total_header": "Total Ligne", "no_results_found": "Aucun résultat trouvé pour ces critères.", "error_loading_history": "Une erreur est survenue lors du chargement de l'historique.", "details_modal_title": "Détails de la Vente", "credits_page_title": "Gestion des Crédits", "debtor_clients_title": "Clients <PERSON>s", "credit_adjustment_button": "Ajustement de Crédit", "client_name_header": "Nom du Client", "due_amount_header": "<PERSON>ant <PERSON>", "pay_button": "Payer", "no_debtor_clients": "Aucun client débiteur.", "record_payment_modal_title": "Enregistrer un Paiement", "client_label_simple": "Client:", "current_debt_label": "<PERSON><PERSON> actuelle:", "payment_amount_label": "Montant du paiement", "manual_credit_modal_title": "Ajustement de Crédit Manuel", "search_client_label": "Rechercher un Client", "selected_client_label": "Client sélectionné:", "no_client_selected": "Aucun", "amount_to_add_label": "Montant à ajouter au crédit", "reason_label": "<PERSON><PERSON><PERSON> (ex: Report ancien solde)", "add_credit_button": "<PERSON><PERSON><PERSON> le Crédit", "invalid_amount_alert": "<PERSON><PERSON> invalide.", "payment_recorded_success": "Paiement enregistré !", "payment_record_failed": "<PERSON><PERSON><PERSON>", "all_fields_required_alert": "Veuillez sélectionner un client et remplir tous les champs avec des valeurs valides.", "add_credit_success": "<PERSON><PERSON><PERSON> a<PERSON> avec succès !", "add_credit_failed": "<PERSON><PERSON><PERSON> lors de l'ajout du crédit", "invoicing_page_title": "Facturation", "invoices_main_title": "Factures", "new_invoice_button": "Nouvelle Facture", "invoice_no_header": "N° Facture", "amount_header": "<PERSON><PERSON>", "view_print_action": "Voir / Imprimer", "no_invoice_found": "Aucune facture trouvée.", "error_loading_invoices": "Erreur de chargement.", "create_invoice_title": "<PERSON><PERSON>er une Facture", "print_pdf_button": "Imprimer/PDF", "invoice_details_title": "Détails de la Facture", "add_line_button": "+ Ajouter une ligne", "error_creating_invoice_editor": "Erreur lors de la création de l'éditeur de facture.", "generic_error_alert": "Une erreur est survenue.", "add_item_to_invoice_alert": "Veuillez ajouter au moins un article à la facture.", "invoice_saved_success": "Facture enregistrée avec succès !", "error_saving_invoice": "Erreur lors de l'enregistrement", "error_generating_invoice": "Impossible de générer la facture.", "error_generating_pdf": "Erreur lors de la génération du PDF.", "settings_page_title": "Paramètres", "appearance_section_title": "Apparence", "app_theme_label": "Thème de l'application", "theme_light": "<PERSON>", "theme_dark": "Sombre", "theme_system": "Système", "app_language_label": "Langue de l'application", "company_info_section_title": "Informations de la Société", "company_name_label": "Nom de la société", "company_address_label": "<PERSON><PERSON><PERSON>", "company_phone_label": "Téléphone", "company_ice_label": "ICE (Identifiant Commun de l'Entreprise)", "company_email_label": "Email", "company_website_label": "Site Web", "save_info_button": "Sauvegarder les informations", "owner_account_security_title": "Sécurité du Compte Propriétaire", "username_label": "Nom d'utilisateur", "current_password_label": "Mot de passe actuel (obligatoire pour toute modification)", "new_password_label": "Nouveau mot de passe (laisser vide pour ne pas changer)", "confirm_new_password_label": "Confirmer le nouveau mot de passe", "update_button": "Mettre à jour", "sellers_management_title": "Gestion des Vendeurs", "password_label": "Mot de passe", "add_seller_button": "<PERSON><PERSON><PERSON>", "username_header": "Nom d'utilisateur", "change_password_button": "Changer <PERSON><PERSON>", "passwords_do_not_match": "Les nouveaux mots de passe ne correspondent pas.", "update_credentials_success": "Vos informations ont été mises à jour avec succès.", "update_credentials_failed": "La mise à jour a échoué", "fill_all_fields_alert": "<PERSON><PERSON><PERSON>z remplir tous les champs.", "seller_added_success": "Vendeur '%s' ajouté avec succès !", "add_seller_failed": "L'ajout a échoué", "confirm_delete_seller": "Êtes-vous sûr de vouloir supprimer ce vendeur ?", "seller_deleted_success": "Vendeur supprimé avec succès.", "delete_seller_failed": "La suppression a échoué", "enter_new_password_for": "Entrez le nouveau mot de passe pour le vendeur '%s':", "password_updated_success": "Mot de passe pour '%s' mis à jour avec succès.", "password_update_failed": "La mise à jour du mot de passe a échoué", "filter_debtors": "Débiteurs", "clear_filters": "<PERSON>ut", "product_analytics": "Analytics Produits", "top_profitable_products": "Produits les Plus Rentables", "top_selling_products": "Produits les Plus Vendus", "performance_overview": "Vue d'Ensemble", "quick_insights": "Recommandations", "profit": "Bénéfice", "margin": "Marge", "quantity_sold": "Quantité Vendue", "revenue": "CA", "sales_frequency": "<PERSON><PERSON><PERSON>", "loading_analytics": "Chargement des analytics...", "no_data_available": "Aucune donnée disponible pour cette période", "view_details": "Voir Détails", "export_data": "Exporter"}