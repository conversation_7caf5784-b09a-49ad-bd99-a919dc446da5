<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="script-src 'self'">
    <title data-i18n="main_menu_products">Produits - Système de Gestion</title>
    <link href="./css/output.css" rel="stylesheet">
    <style>
        .category-results-container { position: absolute; z-index: 60; width: 100%; max-height: 160px; overflow-y: auto; background-color: white; border: 1px solid #d1d5db; border-radius: 0.375rem; box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1); }
        .dark .category-results-container { background-color: #374151; border-color: #4b5563; }
        .category-result-item { padding: 0.75rem; cursor: pointer; }
        .category-result-item:hover { background-color: #f3f4f6; }
        .dark .category-result-item:hover { background-color: #4b5563; }

        /* Styles pour les badges de stock */
        .stock-badge { display: inline-flex; align-items: center; gap: 0.375rem; padding: 0.25rem 0.75rem; border-radius: 9999px; font-size: 0.75rem; font-weight: 600; }
        .stock-badge.in-stock { background-color: #dcfce7; color: #166534; }
        .stock-badge.alert { background-color: #fef3c7; color: #92400e; }
        .stock-badge.out-of-stock { background-color: #fee2e2; color: #991b1b; }
        .dark .stock-badge.in-stock { background-color: #14532d; color: #bbf7d0; }
        .dark .stock-badge.alert { background-color: #451a03; color: #fde68a; }
        .dark .stock-badge.out-of-stock { background-color: #450a0a; color: #fecaca; }

        /* Styles pour les filtres */
        .filter-btn.active { background-color: #dbeafe !important; color: #1d4ed8 !important; border-color: #3b82f6 !important; }
        .dark .filter-btn.active { background-color: #1e3a8a !important; color: #93c5fd !important; border-color: #3b82f6 !important; }

        /* Animation pour les lignes du tableau */
        tbody tr { transition: all 0.2s ease-in-out; }
        tbody tr:hover { background-color: #f8fafc; transform: translateY(-1px); box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1); }
        .dark tbody tr:hover { background-color: #1e293b; }

        /* Styles pour les boutons d'action */
        .action-btn { display: inline-flex; align-items: center; gap: 0.25rem; padding: 0.375rem 0.75rem; border-radius: 0.375rem; font-size: 0.75rem; font-weight: 500; transition: all 0.2s ease-in-out; }
        .action-btn:hover { transform: translateY(-1px); box-shadow: 0 2px 4px -1px rgb(0 0 0 / 0.1); }
        .action-btn.edit { background-color: #dbeafe; color: #1d4ed8; }
        .action-btn.edit:hover { background-color: #bfdbfe; }
        .action-btn.delete { background-color: #fee2e2; color: #991b1b; }
        .action-btn.delete:hover { background-color: #fecaca; }
        .dark .action-btn.edit { background-color: #1e3a8a; color: #93c5fd; }
        .dark .action-btn.edit:hover { background-color: #1e40af; }
        .dark .action-btn.delete { background-color: #450a0a; color: #fecaca; }
        .dark .action-btn.delete:hover { background-color: #7f1d1d; }

        /* Animation de chargement */
        .loading-skeleton { background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%); background-size: 200% 100%; animation: loading 1.5s infinite; }
        @keyframes loading { 0% { background-position: 200% 0; } 100% { background-position: -200% 0; } }
        .dark .loading-skeleton { background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%); background-size: 200% 100%; }
    </style>
</head>
<body class="bg-gray-100 dark:bg-gray-900 flex h-screen">

    <!-- La sidebar sera créée dynamiquement par sidebar-manager.js -->

    <main class="flex-1 p-8 overflow-y-auto">
        <div class="flex flex-col lg:flex-row lg:justify-between lg:items-center mb-6 gap-4">
            <div>
                <h1 class="text-3xl font-bold text-gray-800 dark:text-white mb-2" data-i18n="main_menu_products">Gestion des Produits</h1>
                <div id="productStats" class="flex flex-wrap gap-4 text-sm">
                    <div class="flex items-center gap-2">
                        <span class="w-3 h-3 bg-blue-500 rounded-full"></span>
                        <span class="text-gray-600 dark:text-gray-400">Total: <span id="totalProducts" class="font-semibold text-gray-800 dark:text-white">0</span></span>
                    </div>
                    <div class="flex items-center gap-2">
                        <span class="w-3 h-3 bg-green-500 rounded-full"></span>
                        <span class="text-gray-600 dark:text-gray-400">En stock: <span id="inStockProducts" class="font-semibold text-green-600">0</span></span>
                    </div>
                    <div class="flex items-center gap-2">
                        <span class="w-3 h-3 bg-yellow-500 rounded-full"></span>
                        <span class="text-gray-600 dark:text-gray-400">Alerte: <span id="alertProducts" class="font-semibold text-yellow-600">0</span></span>
                    </div>
                    <div class="flex items-center gap-2">
                        <span class="w-3 h-3 bg-red-500 rounded-full"></span>
                        <span class="text-gray-600 dark:text-gray-400">Rupture: <span id="outOfStockProducts" class="font-semibold text-red-600">0</span></span>
                    </div>
                </div>
            </div>
            <button id="addProductBtn" class="bg-gradient-to-r from-blue-600 to-blue-700 text-white px-6 py-3 rounded-lg hover:from-blue-700 hover:to-blue-800 transition-all duration-200 shadow-lg hover:shadow-xl flex items-center gap-2" data-i18n="add_product_button">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                </svg>
                Ajouter un Produit
            </button>
        </div>

        <div class="mb-6 bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
            <div class="flex flex-col lg:flex-row gap-4">
                <!-- Barre de recherche améliorée -->
                <div class="flex-1 relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </div>
                    <input type="text" id="searchInput" class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white" data-i18n-placeholder="search_by_name_or_barcode" placeholder="Rechercher par nom ou code-barres...">
                </div>

                <!-- Filtres rapides -->
                <div class="flex flex-wrap gap-2">
                    <button id="filterAll" class="filter-btn active px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 bg-blue-100 text-blue-700 border border-blue-200 hover:bg-blue-200">
                        Tous
                    </button>
                    <button id="filterInStock" class="filter-btn px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 bg-gray-100 text-gray-700 border border-gray-200 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-600">
                        En stock
                    </button>
                    <button id="filterAlert" class="filter-btn px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 bg-gray-100 text-gray-700 border border-gray-200 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-600">
                        Alerte
                    </button>
                    <button id="filterOutOfStock" class="filter-btn px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 bg-gray-100 text-gray-700 border border-gray-200 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-600">
                        Rupture
                    </button>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 shadow-lg rounded-lg overflow-hidden border border-gray-200 dark:border-gray-700">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-700 dark:to-gray-800">
                    <tr>
                        <th class="px-6 py-4 text-left text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider" data-i18n="product_name_header">
                            <div class="flex items-center gap-2">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                                </svg>
                                Produit
                            </div>
                        </th>
                        <th class="px-6 py-4 text-left text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider" data-i18n="barcode_header">
                            <div class="flex items-center gap-2">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V6a1 1 0 00-1-1H5a1 1 0 00-1 1v1a1 1 0 001 1z"></path>
                                </svg>
                                Code-barres
                            </div>
                        </th>
                        <th class="px-6 py-4 text-left text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider" data-i18n="retail_price_header">
                            <div class="flex items-center gap-2">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                </svg>
                                Prix Détail
                            </div>
                        </th>
                        <th class="px-6 py-4 text-left text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider" data-i18n="wholesale_price_header">
                            <div class="flex items-center gap-2">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                </svg>
                                Prix Gros
                            </div>
                        </th>
                        <th class="px-6 py-4 text-left text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider" data-i18n="stock_header">
                            <div class="flex items-center gap-2">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                                </svg>
                                Stock
                            </div>
                        </th>
                        <th class="px-6 py-4 text-right text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider" data-i18n="actions_header">
                            <div class="flex items-center justify-end gap-2">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"></path>
                                </svg>
                                Actions
                            </div>
                        </th>
                    </tr>
                </thead>
                <tbody id="productsTableBody" class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"></tbody>
            </table>
        </div>
    </main>

    <div id="productModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center z-40">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl p-8 w-full max-w-3xl z-50">
            <h2 id="modalTitle" class="text-2xl font-bold mb-6">Ajouter un Produit</h2>
            <form id="productForm">
                <input type="hidden" id="productId">
                <input type="hidden" id="imagePath">
                <div class="flex flex-col md:flex-row gap-8">
                    <div class="w-full md:w-1/3 flex flex-col items-center">
                        <img id="imagePreview" src="assets/placeholder.png" alt="Aperçu du produit" class="w-40 h-40 object-cover rounded-lg border dark:border-gray-600 mb-4">
                        <button type="button" id="chooseImageBtn" class="w-full bg-gray-200 dark:bg-gray-600 py-2 px-4 rounded-lg text-sm hover:bg-gray-300 dark:hover:bg-gray-500" data-i18n="choose_image_button">Choisir une image...</button>
                    </div>
                    <div class="w-full md:w-2/3 grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <div class="sm:col-span-2">
                            <label for="name" class="block text-sm font-medium" data-i18n="product_name_label">Nom du produit</label>
                            <input type="text" id="name" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm dark:bg-gray-700 dark:border-gray-600 p-2" required>
                        </div>
                        <div>
                            <label for="barcode" class="flex items-center gap-2 text-sm font-medium mb-2" data-i18n="barcode_label">
                                <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h2M4 4h5m0 0v5m0 0h5m0 0V4m0 0h5m0 5v5"></path>
                                </svg>
                                Code-barres
                            </label>
                            <div class="relative">
                                <input type="text" id="barcode" class="mt-1 block w-full border-2 border-gray-300 rounded-md shadow-sm dark:bg-gray-700 dark:border-gray-600 p-2 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 dark:focus:ring-blue-800 transition-all" data-i18n-placeholder="barcode_scanner_placeholder" placeholder="Scannez ou tapez le code-barres...">
                                <div class="absolute right-2 top-1/2 transform -translate-y-1/2">
                                    <div id="productBarcodeStatus" class="flex items-center gap-1">
                                        <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                                        <span class="text-xs text-gray-500 dark:text-gray-400" data-i18n="scanner_ready">Prêt</span>
                                    </div>
                                </div>
                            </div>
                            <div id="productBarcodeFeedback" class="mt-1 text-sm hidden">
                                <div class="flex items-center gap-2 p-2 rounded-lg">
                                    <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    <span id="productBarcodeMessage"></span>
                                </div>
                            </div>
                        </div>
                        <div class="relative" id="categoryContainer">
                            <label for="category" class="block text-sm font-medium" data-i18n="category_label">Catégorie</label>
                            <input type="text" id="category" autocomplete="off" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm dark:bg-gray-700 dark:border-gray-600 p-2">
                            <div id="categoryResults" class="category-results-container hidden"></div>
                        </div>
                        <div id="purchasePriceContainer">
                            <label for="purchase_price" class="block text-sm font-medium" data-i18n="purchase_price_label">Prix d'Achat</label>
                            <input type="number" step="0.01" id="purchase_price" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm dark:bg-gray-700 dark:border-gray-600 p-2">
                        </div>
                        <div>
                            <label for="price_retail" class="block text-sm font-medium" data-i18n="retail_price_label">Prix Détail</label>
                            <input type="number" step="0.01" id="price_retail" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm dark:bg-gray-700 dark:border-gray-600 p-2" required>
                        </div>
                        <div>
                            <label for="price_wholesale" class="block text-sm font-medium" data-i18n="wholesale_price_label">Prix Gros</label>
                            <input type="number" step="0.01" id="price_wholesale" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm dark:bg-gray-700 dark:border-gray-600 p-2" required>
                        </div>
                        <div>
                            <label for="price_carton" class="block text-sm font-medium" data-i18n="carton_price_label">Prix Carton</label>
                            <input type="number" step="0.01" id="price_carton" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm dark:bg-gray-700 dark:border-gray-600 p-2">
                        </div>
                        <div>
                            <label for="pieces_per_carton" class="block text-sm font-medium" data-i18n="pieces_per_carton_label">Pièces par Carton</label>
                            <input type="number" id="pieces_per_carton" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm dark:bg-gray-700 dark:border-gray-600 p-2">
                        </div>
                        <div>
                            <label for="stock" class="block text-sm font-medium" data-i18n="stock_label">Stock</label>
                            <input type="number" id="stock" name="stock" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm dark:bg-gray-700 dark:border-gray-600" required>
                        </div>
                        <div>
                            <label for="alert_threshold" class="block text-sm font-medium" data-i18n="alert_threshold_label">Seuil d'alerte</label>
                            <input type="number" id="alert_threshold" name="alert_threshold" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm dark:bg-gray-700 dark:border-gray-600" required>
                        </div>
                    </div>
                </div>
                <div class="mt-8 flex justify-end space-x-4">
                    <button type="button" id="cancelBtn" class="bg-gray-200 dark:bg-gray-600 text-gray-800 dark:text-white px-4 py-2 rounded-lg hover:bg-gray-300" data-i18n="cancel_button">Annuler</button>
                    <button type="submit" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700" data-i18n="save_button">Sauvegarder</button>
                </div>
            </form>
        </div>
    </div>
    
    <div id="confirmationModal" class="fixed inset-0 bg-gray-800 bg-opacity-60 hidden items-center justify-center z-50">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl p-6 w-full max-w-sm">
            <h3 id="confirmationMessage" class="text-lg font-semibold mb-4">Êtes-vous sûr ?</h3>
            <div class="flex justify-end gap-4">
                <button id="confirmCancelBtn" class="bg-gray-300 dark:bg-gray-600 px-4 py-2 rounded-lg hover:bg-gray-400" data-i18n="cancel_button">Annuler</button>
                <button id="confirmOkBtn" class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700" data-i18n="confirm_button">Confirmer</button>
            </div>
        </div>
    </div>
    
    <script src="./js/i18n.js"></script>
    <script src="./js/preloader.js"></script>
    <script src="./js/sidebar-manager.js"></script>
    <script src="./js/page-initializer.js"></script>
    <script src="./js/notifications.js"></script>
    <script src="./js/layout.js"></script>
    <script src="./js/products.js"></script>
</body>
</html>