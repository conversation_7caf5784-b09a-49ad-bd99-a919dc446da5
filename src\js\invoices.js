document.addEventListener('DOMContentLoaded', async () => {
    // --- Initialisation de la traduction ---
    await window.i18n.loadTranslations();
    window.i18n.applyTranslationsToHTML();
    const t = window.i18n.t;

    // La fonction showNotification est maintenant disponible globalement via notifications.js

    // --- Vérification des API ---
    if (!window.api) { document.body.innerHTML = "<h1>ERREUR: API manquante.</h1>"; return; }

    // --- Éléments du DOM ---
    const listView = document.getElementById('listView');
    const editorView = document.getElementById('editorView');
    const newInvoiceBtn = document.getElementById('newInvoiceBtn');
    const backToListBtn = document.getElementById('backToListBtn');
    const saveInvoiceBtn = document.getElementById('saveInvoiceBtn');
    const printInvoiceBtn = document.getElementById('printInvoiceBtn');
    const invoicesTableBody = document.getElementById('invoicesTableBody');
    const invoiceEditor = document.getElementById('invoice-editor');
    const editorTitle = document.getElementById('editorView').querySelector('h1');
    
    let currentInvoiceId = null;

    // --- Fonctions ---
    const showListView = () => { currentInvoiceId = null; listView.classList.remove('hidden'); editorView.classList.add('hidden'); loadInvoices(); /* Pas de focus automatique pour éviter les conflits */ };
    const showEditorView = () => { listView.classList.add('hidden'); editorView.classList.remove('hidden'); /* Pas de focus automatique pour éviter les conflits */ };

    async function loadInvoices() {
        try {
            const invoices = await window.api.invoices.getAll();
            invoicesTableBody.innerHTML = '';
            if (invoices.length === 0) {
                invoicesTableBody.innerHTML = `<tr><td colspan="5" class="text-center py-8 text-gray-500">${t('no_invoice_found')}</td></tr>`;
                return;
            }
            invoices.forEach(inv => {
                const tr = document.createElement('tr');
                tr.innerHTML = `<td class="px-6 py-4">${inv.invoice_number}</td><td class="px-6 py-4">${new Date(inv.invoice_date).toLocaleDateString('fr-FR')}</td><td class="px-6 py-4">${inv.client_name}</td><td class="px-6 py-4 font-medium">${inv.total_amount.toFixed(2)} MAD</td><td class="px-6 py-4 text-right"><button class="text-blue-600 hover:underline view-invoice-btn" data-id="${inv.id}">${t('view_print_action')}</button></td>`;
                invoicesTableBody.appendChild(tr);
            });
        } catch (error) { console.error("Erreur chargement factures:", error); invoicesTableBody.innerHTML = `<tr><td colspan="5" class="text-center py-8 text-red-500">${t('error_loading_invoices')}</td></tr>`; }
    }

    function createRowHTML(item, isReadOnly) {
        const defaultPrice = item.unit_price ? item.unit_price.toFixed(2) : '0.00';
        const defaultQty = item.quantity || 1;
        const defaultDesc = item.description || '';
        let displayName = defaultDesc;
        if (item.unit === 'carton') { displayName += ' (Carton)'; } else if (item.unit === 'wholesale') { displayName += ' (Gros)';}
        const unitButtons = isReadOnly ? '' : `<div class="flex flex-col gap-1"><button type="button" title="Prix Détail" class="px-2 h-1/2 rounded-t text-xs ${item.unit === 'retail' || !item.unit ? 'bg-blue-600 text-white' : 'bg-gray-200 dark:bg-gray-700'} set-price-btn" data-type="retail">D</button><button type="button" title="Prix Gros" class="px-2 h-1/2 text-xs ${item.unit === 'wholesale' ? 'bg-blue-600 text-white' : 'bg-gray-200 dark:bg-gray-700'} set-price-btn" data-type="wholesale">G</button><button type="button" title="Prix Carton" class="px-2 h-1/2 rounded-b text-xs ${item.unit === 'carton' ? 'bg-blue-600 text-white' : 'bg-gray-200 dark:bg-gray-700'} set-price-btn" data-type="carton" ${!item.price_carton || item.price_carton <= 0 ? 'disabled' : ''}>C</button></div>`;
        return `<tr class="invoice-item-row" data-price-retail="0" data-price-wholesale="0" data-price-carton="0" data-unit="${item.unit || 'retail'}"><td class="relative align-top"><input type="text" class="w-full p-1 border rounded dark:bg-gray-700 dark:border-gray-600 description-input" value="${isReadOnly ? displayName : defaultDesc}" ${isReadOnly ? 'readonly' : ''} name="description" autocomplete="off"><div class="search-results-container hidden"></div></td><td class="align-top"><input type="number" class="w-20 p-1 border rounded dark:bg-gray-700 dark:border-gray-600" value="${defaultQty}" ${isReadOnly ? 'readonly' : ''} name="quantity"></td><td class="align-top"><div class="flex items-center gap-1"><input type="number" step="0.01" class="w-24 p-1 border rounded dark:bg-gray-700 dark:border-gray-600" value="${defaultPrice}" ${isReadOnly ? 'readonly' : ''} name="unit_price">${unitButtons}</div></td><td class="line-total text-right pr-2 align-top">${(defaultQty * (item.unit_price || 0)).toFixed(2)} MAD</td><td class="align-top">${!isReadOnly ? '<button type="button" class="text-red-500 remove-item-btn">X</button>' : ''}</td></tr>`;
    }

    async function createInvoiceEditor(invoiceId = null) {
        currentInvoiceId = invoiceId;
        let invoiceData, isReadOnly = false;
        try {
            if (invoiceId) {
                invoiceData = await window.api.invoices.getDetails(invoiceId);
                isReadOnly = true;
                editorTitle.textContent = t('invoice_details_title');
                saveInvoiceBtn.classList.add('hidden');
                printInvoiceBtn.classList.remove('hidden');
            } else {
                const nextNumber = await window.api.invoices.getNextNumber();
                invoiceData = { items: [{ description: '', quantity: 1, unit_price: 0, line_total: 0, unit: 'retail' }], invoice_number: nextNumber, invoice_date: new Date().toISOString().split('T')[0], client_name: '', client_address: '', client_phone: '', client_ice: '' };
                editorTitle.textContent = t('create_invoice_title');
                saveInvoiceBtn.classList.remove('hidden');
                printInvoiceBtn.classList.add('hidden');
            }
            const itemsHtml = invoiceData.items.map(item => createRowHTML(item, isReadOnly)).join('');
            invoiceEditor.innerHTML = `<div class="flex justify-between mb-8"><div><h2 class="text-2xl font-bold">FACTURE</h2><p>N°: <input type="text" name="invoice_number" value="${invoiceData.invoice_number}" class="font-bold border-b dark:bg-transparent" readonly></p><p>Date: <input type="date" name="invoice_date" value="${invoiceData.invoice_date}" class="border-b dark:bg-transparent" ${isReadOnly ? 'readonly' : ''}></p></div><div id="clientDetailsContainer" class="text-right w-1/3"><h3 class="font-bold mb-2">Facturé à:</h3><div class="relative mb-2"><input type="text" id="clientSearchInput" placeholder="Rechercher un client..." class="w-full p-1 border rounded dark:bg-gray-700 dark:border-gray-600" autocomplete="off" ${isReadOnly ? 'disabled' : ''}><div id="clientSearchResults" class="search-results-container hidden text-left"></div></div><input type="text" name="client_name" placeholder="Nom du client" class="w-full p-1 border rounded mb-1 dark:bg-gray-700 dark:border-gray-600" value="${invoiceData.client_name || ''}" ${isReadOnly ? 'readonly' : ''}><input type="text" name="client_phone" placeholder="Téléphone" class="w-full p-1 border rounded mb-1 dark:bg-gray-700 dark:border-gray-600" value="${invoiceData.client_phone || ''}" ${isReadOnly ? 'readonly' : ''}><input type="text" name="client_ice" placeholder="ICE" class="w-full p-1 border rounded mb-1 dark:bg-gray-700 dark:border-gray-600" value="${invoiceData.client_ice || ''}" ${isReadOnly ? 'readonly' : ''}><textarea name="client_address" placeholder="Adresse du client" class="w-full p-1 border rounded dark:bg-gray-700 dark:border-gray-600" ${isReadOnly ? 'readonly' : ''}>${invoiceData.client_address || ''}</textarea></div></div><table class="w-full"><thead><tr class="bg-gray-100 dark:bg-gray-700"><th class="text-left p-2">Description</th><th class="text-left p-2">Qté</th><th class="text-left p-2">Prix U.</th><th class="text-right p-2">Total</th><th></th></tr></thead><tbody id="invoiceItemsTable">${itemsHtml}</tbody></table>${!isReadOnly ? `<button type="button" id="addItemBtn" class="mt-4 bg-gray-200 dark:bg-gray-600 px-3 py-1 rounded hover:bg-gray-300 dark:hover:bg-gray-500">${t('add_line_button')}</button>` : ''}<div class="flex justify-end mt-8"><div class="w-1/2 sm:w-1/3"><div class="flex justify-between font-bold text-xl"><p>TOTAL HT</p><p id="total-ht">0.00 MAD</p></div></div></div>`;
            calculateTotals();
            showEditorView();
        } catch (error) { console.error("Erreur createInvoiceEditor:", error); showNotification(t('error_creating_invoice_editor'), 'error'); }
    }

    function calculateTotals() { let total = 0; document.querySelectorAll('.invoice-item-row').forEach(row => { const qty = parseFloat(row.querySelector('[name="quantity"]').value) || 0; const price = parseFloat(row.querySelector('[name="unit_price"]').value) || 0; row.querySelector('.line-total').textContent = `${(qty * price).toFixed(2)} MAD`; total += (qty * price); }); document.getElementById('total-ht').textContent = `${total.toFixed(2)} MAD`; }
    async function generatePrintableInvoice() { const invoiceData = await window.api.invoices.getDetails(currentInvoiceId); if (!invoiceData) return ''; const companyInfo = await window.api.settings.getCompanyInfo(); const itemsRows = invoiceData.items.map(item => { const displayName = item.unit === 'carton' ? `${item.description} (Carton)` : item.description; return `<tr class="item"><td>${displayName}</td><td>${item.quantity}</td><td>${item.unit_price.toFixed(2)} MAD</td><td class="text-right">${item.line_total.toFixed(2)} MAD</td></tr>` }).join(''); return `<!DOCTYPE html><html lang="fr"><head><meta charset="UTF-8"><style>body{font-family:-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,sans-serif;margin:0;padding:20px;font-size:12px;}.invoice-box{max-width:800px;margin:auto;padding:30px;border:1px solid #eee;box-shadow:0 0 10px rgba(0,0,0,.15);}.header{display:flex;justify-content:space-between;margin-bottom:40px;}.header .company-details,.header .invoice-details{width:48%;}.client-details{margin-bottom:40px;}.invoice-table{width:100%;border-collapse:collapse;}.invoice-table th,.invoice-table td{border-bottom:1px solid #eee;padding:8px;}.invoice-table th{background-color:#f7f7f7;text-align:left;}.invoice-table .item td{vertical-align:top;}.totals{margin-top:30px;float:right;width:40%;}.totals table{width:100%;}.totals td{padding:5px;}.text-right{text-align:right;}.font-bold{font-weight:bold;}</style></head><body><div class="invoice-box"><div class="header"><div class="company-details"><h1 class="font-bold" style="font-size:24px;">${companyInfo.name || 'Votre Société'}</h1><p>${(companyInfo.address || 'Votre Adresse').replace(/\n/g, '<br>')}<br>${companyInfo.phone ? `Tél: ${companyInfo.phone}<br>` : ''}${companyInfo.ice ? `ICE: ${companyInfo.ice}` : ''}</p></div><div class="invoice-details text-right"><h2 class="font-bold" style="font-size:32px;">FACTURE</h2><p><span class="font-bold">N°:</span> ${invoiceData.invoice_number}</p><p><span class="font-bold">Date:</span> ${new Date(invoiceData.invoice_date).toLocaleDateString('fr-FR')}</p></div></div><div class="client-details"><h3 class="font-bold">Facturé à:</h3><p>${invoiceData.client_name}<br>${(invoiceData.client_address || '').replace(/\n/g, '<br>')}<br>${invoiceData.client_phone ? `Tél: ${invoiceData.client_phone}<br>` : ''}${invoiceData.client_ice ? `ICE: ${invoiceData.client_ice}` : ''}</p></div><table class="invoice-table"><thead><tr><th>Description</th><th>Qté</th><th>Prix U.</th><th class="text-right">Total</th></tr></thead><tbody>${itemsRows}</tbody></table><div class="totals"><table><tr><td class="font-bold" style="font-size:18px;">TOTAL</td><td class="text-right font-bold" style="font-size:18px;">${invoiceData.total_amount.toFixed(2)} MAD</td></tr></table></div></div></body></html>`; }
    
    // --- Écouteurs d'événements ---
    newInvoiceBtn.addEventListener('click', () => createInvoiceEditor());
    backToListBtn.addEventListener('click', showListView);
    invoicesTableBody.addEventListener('click', e => { const viewBtn = e.target.closest('.view-invoice-btn'); if (viewBtn) createInvoiceEditor(viewBtn.dataset.id); });
    document.addEventListener('click', (e) => { if (!e.target.closest('.description-input')) { document.querySelectorAll('.search-results-container').forEach(c => c.classList.add('hidden')); } if (!e.target.closest('#clientDetailsContainer')) { const clientResults = document.getElementById('clientSearchResults'); if (clientResults) clientResults.classList.add('hidden'); } });
    invoiceEditor.addEventListener('input', async (e) => { if (e.target.classList.contains('description-input')) { const input = e.target; const searchTerm = input.value; const resultsContainer = input.nextElementSibling; if (searchTerm.length < 2) { resultsContainer.classList.add('hidden'); return; } try { const products = await window.api.products.getAll(searchTerm); resultsContainer.innerHTML = ''; if (products.length > 0) { products.forEach(p => { const itemDiv = document.createElement('div'); itemDiv.className = 'search-result-item p-2 hover:bg-gray-200 dark:hover:bg-gray-600 cursor-pointer'; itemDiv.textContent = p.name; itemDiv.dataset.product = JSON.stringify(p); resultsContainer.appendChild(itemDiv); }); resultsContainer.classList.remove('hidden'); } else { resultsContainer.classList.add('hidden'); } } catch (error) { console.error("Erreur recherche produit:", error); } } if (e.target.id === 'clientSearchInput') { const searchTerm = e.target.value; const resultsContainer = document.getElementById('clientSearchResults'); if (searchTerm.length < 2) { resultsContainer.classList.add('hidden'); return; } const clients = await window.api.clients.getAll(searchTerm); resultsContainer.innerHTML = ''; if(clients.length > 0){ clients.forEach(c => { const itemDiv = document.createElement('div'); itemDiv.className = 'search-result-item p-2 hover:bg-gray-200 dark:hover:bg-gray-600 cursor-pointer'; itemDiv.textContent = c.name; itemDiv.dataset.client = JSON.stringify(c); resultsContainer.appendChild(itemDiv); }); resultsContainer.classList.remove('hidden');} } if (e.target.name === 'quantity' || e.target.name === 'unit_price') { calculateTotals(); } });
    invoiceEditor.addEventListener('click', (e) => { if (e.target.closest('.search-result-item')) { const selectedItem = e.target.closest('.search-result-item'); if (selectedItem.dataset.product) { const product = JSON.parse(selectedItem.dataset.product); const parentRow = selectedItem.closest('tr'); parentRow.querySelector('[name="description"]').value = product.name; parentRow.querySelector('[name="unit_price"]').value = product.price_retail.toFixed(2); parentRow.dataset.priceRetail = product.price_retail; parentRow.dataset.priceWholesale = product.price_wholesale; parentRow.dataset.priceCarton = product.price_carton; parentRow.dataset.unit = 'retail'; const cartonBtn = parentRow.querySelector('.set-price-btn[data-type="carton"]'); if (cartonBtn) { cartonBtn.disabled = !product.price_carton || product.price_carton <= 0; } selectedItem.parentElement.classList.add('hidden'); calculateTotals(); parentRow.querySelector('[name="quantity"]').focus(); } if (selectedItem.dataset.client) { const client = JSON.parse(selectedItem.dataset.client); document.querySelector('[name="client_name"]').value = client.name; document.querySelector('[name="client_phone"]').value = client.phone || ''; document.querySelector('[name="client_ice"]').value = client.ice || ''; document.querySelector('[name="client_address"]').value = client.address || ''; selectedItem.parentElement.classList.add('hidden'); } } if (e.target.classList.contains('set-price-btn')) { const btn = e.target; const parentRow = btn.closest('tr'); const priceInput = parentRow.querySelector('[name="unit_price"]'); const type = btn.dataset.type; let priceToSet = 0; if (type === 'retail') priceToSet = parentRow.dataset.priceRetail; else if (type === 'wholesale') priceToSet = parentRow.dataset.priceWholesale; else if (type === 'carton') priceToSet = parentRow.dataset.priceCarton; if (priceToSet > 0) { priceInput.value = parseFloat(priceToSet).toFixed(2); parentRow.dataset.unit = type; parentRow.querySelectorAll('.set-price-btn').forEach(b => { b.classList.remove('bg-blue-600', 'text-white'); b.classList.add('bg-gray-200', 'dark:bg-gray-700'); }); btn.classList.add('bg-blue-600', 'text-white'); btn.classList.remove('bg-gray-200', 'dark:bg-gray-700'); calculateTotals(); } } if (e.target.id === 'addItemBtn') { const tbody = document.getElementById('invoiceItemsTable'); const newRow = document.createElement('tr'); newRow.innerHTML = createRowHTML({}, false); tbody.appendChild(newRow); newRow.querySelector('.description-input').focus(); } if (e.target.classList.contains('remove-item-btn')) { e.target.closest('tr').remove(); calculateTotals(); } });
    saveInvoiceBtn.addEventListener('click', async () => { const items = []; document.querySelectorAll('.invoice-item-row').forEach(row => { const description = row.querySelector('[name="description"]').value; if (description) { items.push({ description: description, quantity: parseFloat(row.querySelector('[name="quantity"]').value) || 0, unit_price: parseFloat(row.querySelector('[name="unit_price"]').value) || 0, unit: row.dataset.unit || 'piece', line_total: parseFloat(row.querySelector('.line-total').textContent) || 0 }); } }); if (items.length === 0) { alert(t('add_item_to_invoice_alert')); return; } const invoiceData = { invoice_number: document.querySelector('[name="invoice_number"]').value, invoice_date: document.querySelector('[name="invoice_date"]').value, client_name: document.querySelector('[name="client_name"]').value, client_phone: document.querySelector('[name="client_phone"]').value, client_ice: document.querySelector('[name="client_ice"]').value, client_address: document.querySelector('[name="client_address"]').value, total_amount: parseFloat(document.getElementById('total-ht').textContent), items: items }; try { await window.api.invoices.create(invoiceData); alert(t('invoice_saved_success')); showListView(); } catch(error) { alert(`${t('error_saving_invoice')}: ${error.message}`); } });
    printInvoiceBtn.addEventListener('click', async () => { const invoiceHTML = await generatePrintableInvoice(); if (!invoiceHTML) { showNotification(t('error_generating_invoice'), 'error'); return; } try { const pdfData = await window.api.print.toPDF(invoiceHTML); const blob = new Blob([pdfData], { type: 'application/pdf' }); const url = URL.createObjectURL(blob); const a = document.createElement('a'); const invoiceDetails = await window.api.invoices.getDetails(currentInvoiceId); a.download = `${invoiceDetails.invoice_number.replace(/\//g, '-')}.pdf`; a.href = url; a.click(); setTimeout(() => URL.revokeObjectURL(url), 100); } catch (error) { console.error("Erreur PDF:", error); showNotification(t('error_generating_pdf'), 'error'); } });

    // --- Initialisation de la Page ---
    async function initPage() {
        if(typeof initializePage === 'function'){ await initializePage('invoices'); }
        const user = await window.api.session.getCurrentUser();
        if (!user || user.role !== 'Propriétaire') {
            document.body.innerHTML = `<div class="w-full h-screen flex items-center justify-center bg-gray-100 dark:bg-gray-900"><h1 class='text-red-500 text-2xl font-bold'>${t('owner_only_access')}</h1></div>`;
            return;
        }
        loadInvoices();
    }
    initPage();
});