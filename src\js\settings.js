document.addEventListener('DOMContentLoaded', async () => {
    // --- Initialisation de la traduction ---
    await window.i18n.loadTranslations();
    window.i18n.applyTranslationsToHTML();
    const t = window.i18n.t;

    // La fonction showNotification est maintenant disponible globalement via notifications.js

    // --- Vérification des API ---
    if (!window.api) { document.body.innerHTML = "<h1>ERREUR: API non disponible.</h1>"; return; }

    // --- Navigation entre sections ---
    function initSectionNavigation() {
        const navButtons = document.querySelectorAll('.section-nav-btn');
        const sections = {
            'appearance': document.getElementById('appearance-section'),
            'company': document.getElementById('company-section'),
            'security': document.getElementById('security-section'),
            'users': document.getElementById('users-section')
        };

        function showSection(sectionName) {
            // Masquer toutes les sections
            Object.values(sections).forEach(section => {
                if (section) section.classList.add('hidden');
            });

            // Afficher la section sélectionnée
            if (sections[sectionName]) {
                sections[sectionName].classList.remove('hidden');
            }

            // Mettre à jour les boutons de navigation
            navButtons.forEach(btn => {
                btn.classList.remove('active', 'bg-blue-500', 'text-white');
                btn.classList.add('text-gray-600', 'dark:text-gray-400');
                if (btn.dataset.section === sectionName) {
                    btn.classList.add('active', 'bg-blue-500', 'text-white');
                    btn.classList.remove('text-gray-600', 'dark:text-gray-400');
                }
            });
        }

        // Ajouter les event listeners
        navButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                showSection(btn.dataset.section);
            });
        });

        // Afficher la première section par défaut
        showSection('appearance');
    }

    // --- Amélioration de l'indicateur de force du mot de passe ---
    function initPasswordStrengthIndicator() {
        const newPasswordInput = document.getElementById('newPassword');
        const strengthIndicator = document.getElementById('password-strength');

        if (newPasswordInput && strengthIndicator) {
            newPasswordInput.addEventListener('input', (e) => {
                const password = e.target.value;
                const strength = calculatePasswordStrength(password);
                updatePasswordStrengthIndicator(strength);
            });
        }
    }

    function calculatePasswordStrength(password) {
        let score = 0;
        if (password.length >= 8) score += 25;
        if (password.match(/[a-z]/)) score += 25;
        if (password.match(/[A-Z]/)) score += 25;
        if (password.match(/[0-9]/)) score += 15;
        if (password.match(/[^a-zA-Z0-9]/)) score += 10;
        return Math.min(score, 100);
    }

    function updatePasswordStrengthIndicator(strength) {
        const strengthIndicator = document.getElementById('password-strength');
        if (!strengthIndicator) return;

        strengthIndicator.style.width = strength + '%';

        if (strength < 30) {
            strengthIndicator.className = 'h-2 bg-red-400 rounded-full transition-all duration-300';
        } else if (strength < 60) {
            strengthIndicator.className = 'h-2 bg-yellow-400 rounded-full transition-all duration-300';
        } else if (strength < 80) {
            strengthIndicator.className = 'h-2 bg-blue-400 rounded-full transition-all duration-300';
        } else {
            strengthIndicator.className = 'h-2 bg-green-400 rounded-full transition-all duration-300';
        }
    }

    // --- Amélioration de l'affichage des utilisateurs ---
    function updateUsersTable() {
        const tableBody = document.getElementById('usersTableBody');
        const noUsersMessage = document.getElementById('no-users-message');

        if (tableBody && tableBody.children.length === 0) {
            if (noUsersMessage) noUsersMessage.classList.remove('hidden');
        } else {
            if (noUsersMessage) noUsersMessage.classList.add('hidden');
        }
    }

    // Initialiser les nouvelles fonctionnalités
    initSectionNavigation();
    initPasswordStrengthIndicator();

    // --- Éléments du DOM ---
    const themeButtons = document.querySelectorAll('.theme-btn');
    const languageSelector = document.getElementById('language-selector');
    const companyInfoForm = document.getElementById('companyInfoForm');
    const ownerSecurityCard = document.getElementById('ownerSecurityCard');
    const ownerCredentialsForm = document.getElementById('ownerCredentialsForm');
    const sellersManagementCard = document.getElementById('sellersManagementCard');
    const addUserForm = document.getElementById('addUserForm');
    const usersTableBody = document.getElementById('usersTableBody');

    // --- GESTION DU THÈME ---
    const updateThemeButtons = (activeTheme) => { themeButtons.forEach(button => { if (button.dataset.theme === activeTheme) { button.classList.add('bg-blue-600', 'text-white'); button.classList.remove('bg-gray-200', 'dark:bg-gray-700'); } else { button.classList.remove('bg-blue-600', 'text-white'); button.classList.add('bg-gray-200', 'dark:bg-gray-700'); } }); };
    themeButtons.forEach(button => { button.addEventListener('click', async () => { const theme = button.dataset.theme; await window.api.theme.set(theme); updateThemeButtons(theme); }); });

    // --- GESTION DE LA LANGUE ---
    languageSelector.addEventListener('change', async () => {
        const selectedLang = languageSelector.value;
        await window.api.settings.language.set(selectedLang);

        // Créer une modal de confirmation non-bloquante
        showLanguageChangeConfirmation(selectedLang);
    });

    function showLanguageChangeConfirmation(selectedLang) {
        // Créer la modal de confirmation
        const confirmModal = document.createElement('div');
        confirmModal.className = 'fixed inset-0 bg-gray-800 bg-opacity-60 flex items-center justify-center z-50';
        confirmModal.innerHTML = `
            <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-6 w-full max-w-md mx-4">
                <div class="flex items-center gap-3 mb-4">
                    <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center">
                        <svg class="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129"></path>
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Changement de langue</h3>
                        <p class="text-sm text-gray-600 dark:text-gray-400">Redémarrage requis</p>
                    </div>
                </div>

                <div class="mb-6">
                    <p class="text-gray-700 dark:text-gray-300 mb-3">
                        Pour appliquer la nouvelle langue <strong>${selectedLang === 'fr' ? 'Français' : 'العربية'}</strong>, l'application doit redémarrer.
                    </p>
                    <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3">
                        <div class="flex items-start gap-2">
                            <svg class="w-4 h-4 text-blue-600 dark:text-blue-400 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <p class="text-sm text-blue-700 dark:text-blue-300">
                                Vos données seront sauvegardées automatiquement avant le redémarrage.
                            </p>
                        </div>
                    </div>
                </div>

                <div class="flex gap-3 justify-end">
                    <button id="cancelLanguageChange" class="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 font-medium transition-colors duration-200">
                        Annuler
                    </button>
                    <button id="confirmLanguageChange" class="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-all duration-200 flex items-center gap-2">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                        Redémarrer maintenant
                    </button>
                </div>
            </div>
        `;

        document.body.appendChild(confirmModal);

        // Event listeners pour les boutons
        const cancelBtn = confirmModal.querySelector('#cancelLanguageChange');
        const confirmBtn = confirmModal.querySelector('#confirmLanguageChange');

        cancelBtn.addEventListener('click', async () => {
            // Restaurer la langue précédente
            try {
                const currentLang = await window.api.settings.language.get();
                languageSelector.value = currentLang || 'fr';
                showNotification('Changement de langue annulé', 'info');
            } catch (error) {
                console.error('Erreur lors de la restauration de la langue:', error);
                languageSelector.value = 'fr'; // Valeur par défaut
            }
            document.body.removeChild(confirmModal);
        });

        confirmBtn.addEventListener('click', async () => {
            try {
                showNotification('Redémarrage de l\'application...', 'info');
                // Petit délai pour que la notification s'affiche
                setTimeout(async () => {
                    await window.api.app.reload();
                }, 500);
            } catch (error) {
                console.error('Erreur lors du redémarrage:', error);
                showNotification('Erreur lors du redémarrage de l\'application', 'error');
            }
            document.body.removeChild(confirmModal);
        });

        // Fermer avec Escape
        const handleEscape = (e) => {
            if (e.key === 'Escape') {
                cancelBtn.click();
                document.removeEventListener('keydown', handleEscape);
            }
        };
        document.addEventListener('keydown', handleEscape);

        // Fermer en cliquant à l'extérieur
        confirmModal.addEventListener('click', (e) => {
            if (e.target === confirmModal) {
                cancelBtn.click();
            }
        });
    }

    // --- GESTION INFOS SOCIÉTÉ ---
    companyInfoForm.addEventListener('submit', async (e) => { e.preventDefault(); const companyData = { name: document.getElementById('company_name').value, address: document.getElementById('company_address').value, phone: document.getElementById('company_phone').value, ice: document.getElementById('company_ice').value, email: document.getElementById('company_email').value, website: document.getElementById('company_website').value, }; try { await window.api.settings.saveCompanyInfo(companyData); showNotification('Informations de la société sauvegardées avec succès !', 'success'); } catch (error) { console.error("Erreur de sauvegarde:", error); showNotification("La sauvegarde a échoué : " + error.message, 'error'); } });

    // --- GESTION SÉCURITÉ PROPRIÉTAIRE ---
    ownerCredentialsForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        const newUsername = document.getElementById('ownerUsername').value;
        const currentPassword = document.getElementById('currentPassword').value;
        const newPassword = document.getElementById('newPassword').value;
        const confirmNewPassword = document.getElementById('confirmNewPassword').value;
        if (newPassword !== confirmNewPassword) { showNotification(t('passwords_do_not_match'), 'warning'); return; }
        try {
            const result = await window.api.users.updateCredentials({ newUsername, currentPassword, newPassword: newPassword || null, });
            showNotification(t(result.message), 'success'); // Assumant que le message est une clé de traduction
            ownerCredentialsForm.reset();
        } catch (error) { console.error("Erreur de mise à jour:", error); showNotification(`${t('update_credentials_failed')}: ${error.message}`, 'error'); }
    });

    // --- GESTION DES VENDEURS ---
    async function loadUsers() {
        try {
            const users = await window.api.users.getAll();
            usersTableBody.innerHTML = '';

            const sellers = users.filter(u => u.role !== 'Propriétaire');

            sellers.forEach(user => {
                const tr = document.createElement('tr');
                tr.className = 'table-row';
                tr.innerHTML = `
                    <td class="px-6 py-4">
                        <div class="flex items-center gap-3">
                            <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-sm font-semibold">
                                ${user.username.charAt(0).toUpperCase()}
                            </div>
                            <div>
                                <div class="font-medium text-gray-900 dark:text-white">${user.username}</div>
                                <div class="text-sm text-gray-500 dark:text-gray-400">Vendeur</div>
                            </div>
                        </div>
                    </td>
                    <td class="px-6 py-4">
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100">
                            <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                            </svg>
                            Actif
                        </span>
                    </td>
                    <td class="px-6 py-4 text-right">
                        <div class="flex items-center justify-end gap-2">
                            <button class="change-password-btn bg-yellow-500 hover:bg-yellow-600 text-white px-3 py-2 rounded-lg text-sm font-medium flex items-center gap-2 transition-all duration-200" data-id="${user.id}" data-username="${user.username}">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m0 0a2 2 0 012 2m-2-2h-3m-1 1L9 6 5 10l4 4"></path>
                                </svg>
                                ${t('change_password_button')}
                            </button>
                            <button class="delete-user-btn danger-btn text-white px-3 py-2 rounded-lg text-sm font-medium flex items-center gap-2" data-id="${user.id}">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                </svg>
                                ${t('delete')}
                            </button>
                        </div>
                    </td>
                `;
                usersTableBody.appendChild(tr);
            });

            // Mettre à jour l'affichage du message "aucun utilisateur"
            updateUsersTable();

        } catch (error) {
            console.error("Erreur chargement vendeurs:", error);
            showNotification("Erreur lors du chargement des vendeurs", 'error');
        }
    }

    addUserForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        const usernameInput = document.getElementById('username');
        const passwordInput = document.getElementById('password');
        if (!usernameInput.value || !passwordInput.value) { showNotification(t('fill_all_fields_alert'), 'warning'); return; }
        try {
            await window.api.users.add({ username: usernameInput.value, password: passwordInput.value });
            showNotification(t('seller_added_success').replace('%s', usernameInput.value), 'success');
            usernameInput.value = ''; passwordInput.value = '';
            loadUsers();
        } catch (error) { console.error("Erreur ajout vendeur:", error); showNotification(`${t('add_seller_failed')}: ${error.message}`, 'error'); }
    });

    usersTableBody.addEventListener('click', async (e) => {
        const target = e.target;
        if (target.classList.contains('delete-user-btn')) {
            const userId = target.dataset.id;
            if (await window.showConfirmation(t('confirm_delete_seller'))) {
                try { await window.api.users.delete(userId); showNotification(t('seller_deleted_success'), 'success'); loadUsers(); }
                catch (error) { showNotification(`${t('delete_seller_failed')}: ${error.message}`, 'error'); }
            }
        }
        if (target.classList.contains('change-password-btn')) {
            const userId = target.dataset.id;
            const username = target.dataset.username;
            showPasswordChangeModal(userId, username);
        }
    });

    // --- MODAL DE CHANGEMENT DE MOT DE PASSE ---
    function showPasswordChangeModal(userId, username) {
        const passwordModal = document.createElement('div');
        passwordModal.className = 'fixed inset-0 bg-gray-800 bg-opacity-60 flex items-center justify-center z-50';
        passwordModal.innerHTML = `
            <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-6 w-full max-w-md mx-4">
                <div class="flex items-center gap-3 mb-4">
                    <div class="w-10 h-10 bg-yellow-100 dark:bg-yellow-900/20 rounded-full flex items-center justify-center">
                        <svg class="w-5 h-5 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m0 0a2 2 0 012 2m-2-2h-3m-1 1L9 6 5 10l4 4"></path>
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Changer le mot de passe</h3>
                        <p class="text-sm text-gray-600 dark:text-gray-400">Utilisateur: ${username}</p>
                    </div>
                </div>

                <form id="passwordChangeForm" class="space-y-4">
                    <div>
                        <label for="newUserPassword" class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2">
                            Nouveau mot de passe
                        </label>
                        <input
                            type="password"
                            id="newUserPassword"
                            class="form-input w-full px-4 py-3 rounded-xl text-sm"
                            placeholder="Entrez le nouveau mot de passe"
                            required
                            minlength="6"
                        >
                    </div>

                    <div>
                        <label for="confirmUserPassword" class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2">
                            Confirmer le mot de passe
                        </label>
                        <input
                            type="password"
                            id="confirmUserPassword"
                            class="form-input w-full px-4 py-3 rounded-xl text-sm"
                            placeholder="Confirmez le nouveau mot de passe"
                            required
                            minlength="6"
                        >
                    </div>

                    <!-- Indicateur de force du mot de passe -->
                    <div>
                        <div class="text-xs text-gray-600 dark:text-gray-400 mb-2">Force du mot de passe:</div>
                        <div class="flex gap-1">
                            <div class="h-2 w-full bg-gray-200 dark:bg-gray-700 rounded-full">
                                <div id="userPasswordStrength" class="h-2 bg-red-400 rounded-full transition-all duration-300" style="width: 0%"></div>
                            </div>
                        </div>
                        <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                            Minimum 6 caractères recommandés
                        </div>
                    </div>

                    <div class="bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg p-3">
                        <div class="flex items-start gap-2">
                            <svg class="w-4 h-4 text-amber-600 dark:text-amber-400 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.268 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                            <p class="text-sm text-amber-700 dark:text-amber-300">
                                Le vendeur devra utiliser ce nouveau mot de passe lors de sa prochaine connexion.
                            </p>
                        </div>
                    </div>

                    <div class="flex gap-3 justify-end pt-4">
                        <button type="button" id="cancelPasswordChange" class="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 font-medium transition-colors duration-200">
                            Annuler
                        </button>
                        <button type="submit" class="bg-gradient-to-r from-yellow-500 to-yellow-600 hover:from-yellow-600 hover:to-yellow-700 text-white px-6 py-2 rounded-lg font-medium transition-all duration-200 flex items-center gap-2">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            Changer le mot de passe
                        </button>
                    </div>
                </form>
            </div>
        `;

        document.body.appendChild(passwordModal);

        // Éléments du formulaire
        const form = passwordModal.querySelector('#passwordChangeForm');
        const newPasswordInput = passwordModal.querySelector('#newUserPassword');
        const confirmPasswordInput = passwordModal.querySelector('#confirmUserPassword');
        const strengthIndicator = passwordModal.querySelector('#userPasswordStrength');
        const cancelBtn = passwordModal.querySelector('#cancelPasswordChange');

        // Indicateur de force du mot de passe
        newPasswordInput.addEventListener('input', (e) => {
            const password = e.target.value;
            const strength = calculatePasswordStrength(password);
            updatePasswordStrengthIndicator(strength, strengthIndicator);
        });

        // Validation en temps réel
        confirmPasswordInput.addEventListener('input', () => {
            if (confirmPasswordInput.value && newPasswordInput.value !== confirmPasswordInput.value) {
                confirmPasswordInput.setCustomValidity('Les mots de passe ne correspondent pas');
            } else {
                confirmPasswordInput.setCustomValidity('');
            }
        });

        // Soumission du formulaire
        form.addEventListener('submit', async (e) => {
            e.preventDefault();

            const newPassword = newPasswordInput.value;
            const confirmPassword = confirmPasswordInput.value;

            if (newPassword !== confirmPassword) {
                showNotification('Les mots de passe ne correspondent pas', 'warning');
                return;
            }

            if (newPassword.length < 6) {
                showNotification('Le mot de passe doit contenir au moins 6 caractères', 'warning');
                return;
            }

            try {
                await window.api.users.updatePassword({ id: userId, password: newPassword });
                showNotification(`Mot de passe mis à jour pour ${username}`, 'success');
                document.body.removeChild(passwordModal);
            } catch (error) {
                console.error('Erreur lors de la mise à jour du mot de passe:', error);
                showNotification(`Erreur lors de la mise à jour: ${error.message}`, 'error');
            }
        });

        // Annulation
        cancelBtn.addEventListener('click', () => {
            document.body.removeChild(passwordModal);
        });

        // Fermer avec Escape
        const handleEscape = (e) => {
            if (e.key === 'Escape') {
                cancelBtn.click();
                document.removeEventListener('keydown', handleEscape);
            }
        };
        document.addEventListener('keydown', handleEscape);

        // Fermer en cliquant à l'extérieur
        passwordModal.addEventListener('click', (e) => {
            if (e.target === passwordModal) {
                cancelBtn.click();
            }
        });

        // Focus sur le premier champ
        setTimeout(() => {
            newPasswordInput.focus();
        }, 100);
    }

    // Fonction utilitaire pour mettre à jour l'indicateur de force
    function updatePasswordStrengthIndicator(strength, indicator) {
        if (!indicator) return;

        indicator.style.width = strength + '%';

        if (strength < 30) {
            indicator.className = 'h-2 bg-red-400 rounded-full transition-all duration-300';
        } else if (strength < 60) {
            indicator.className = 'h-2 bg-yellow-400 rounded-full transition-all duration-300';
        } else if (strength < 80) {
            indicator.className = 'h-2 bg-blue-400 rounded-full transition-all duration-300';
        } else {
            indicator.className = 'h-2 bg-green-400 rounded-full transition-all duration-300';
        }
    }

    // --- INITIALISATION DE LA PAGE ---
    async function initPage() {
        if (typeof initializePage === 'function') { await initializePage('settings'); }
        const currentTheme = await window.api.theme.get();
        updateThemeButtons(currentTheme || 'system');
        const currentLang = await window.api.settings.language.get();
        languageSelector.value = currentLang || 'fr';
        const info = await window.api.settings.getCompanyInfo();
        if (info) { document.getElementById('company_name').value = info.name || ''; document.getElementById('company_address').value = info.address || ''; document.getElementById('company_phone').value = info.phone || ''; document.getElementById('company_ice').value = info.ice || ''; document.getElementById('company_email').value = info.email || ''; document.getElementById('company_website').value = info.website || ''; }
        const currentUser = await window.api.session.getCurrentUser();
        if (currentUser && currentUser.role === 'Propriétaire') {
            ownerSecurityCard.classList.remove('hidden');
            sellersManagementCard.classList.remove('hidden');
            document.getElementById('ownerUsername').value = currentUser.username;
            loadUsers();
        } else {
            // Cacher les sections pour les non-propriétaires si nécessaire
            ownerSecurityCard.classList.add('hidden');
            sellersManagementCard.classList.add('hidden');
        }
    }
    
    initPage();
});