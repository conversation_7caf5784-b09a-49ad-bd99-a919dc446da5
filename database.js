const path = require('path');
const Database = require('better-sqlite3');
const bcrypt = require('bcrypt');
const saltRounds = 10;
const fs = require('fs');

const dbPath = path.join(process.cwd(), 'database', 'main.db');
const dbDir = path.dirname(dbPath);
if (!fs.existsSync(dbDir)) {
    fs.mkdirSync(dbDir, { recursive: true });
}
const db = new Database(dbPath);

function initDatabase() {
    db.exec(`
        PRAGMA foreign_keys = ON;

        CREATE TABLE IF NOT EXISTS products (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            barcode TEXT UNIQUE,
            name TEXT NOT NULL,
            purchase_price REAL NOT NULL DEFAULT 0,
            price_retail REAL NOT NULL,
            price_wholesale REAL NOT NULL,
            price_carton REAL NOT NULL DEFAULT 0,
            stock INTEGER NOT NULL DEFAULT 0,
            alert_threshold INTEGER NOT NULL DEFAULT 0,
            pieces_per_carton INTEGER NOT NULL DEFAULT 0,
            category TEXT,
            image_path TEXT
        );

        CREATE TABLE IF NOT EXISTS clients ( id INTEGER PRIMARY KEY AUTOINCREMENT, name TEXT NOT NULL, phone TEXT, address TEXT, credit_balance REAL NOT NULL DEFAULT 0, ice TEXT );
        CREATE TABLE IF NOT EXISTS users ( id INTEGER PRIMARY KEY AUTOINCREMENT, username TEXT NOT NULL UNIQUE, password_hash TEXT NOT NULL, role TEXT NOT NULL CHECK(role IN ('Propriétaire', 'Vendeur')) );
        CREATE TABLE IF NOT EXISTS sales ( id INTEGER PRIMARY KEY AUTOINCREMENT, client_id INTEGER, user_id INTEGER NOT NULL, total_amount REAL NOT NULL, amount_paid_cash REAL NOT NULL, amount_paid_credit REAL NOT NULL, sale_date TEXT NOT NULL DEFAULT (strftime('%Y-%m-%d %H:%M:%S', 'now', 'localtime')), status TEXT NOT NULL DEFAULT 'COMPLETED' CHECK(status IN ('COMPLETED', 'RETURNED', 'CORRECTED')), original_sale_id INTEGER, FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE SET NULL, FOREIGN KEY (user_id) REFERENCES users(id), FOREIGN KEY (original_sale_id) REFERENCES sales(id) ON DELETE SET NULL );
        
        CREATE TABLE IF NOT EXISTS sale_items (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            sale_id INTEGER NOT NULL,
            product_id INTEGER NOT NULL,
            quantity INTEGER NOT NULL,
            unit TEXT NOT NULL DEFAULT 'piece',
            unit_price REAL NOT NULL,
            line_total REAL NOT NULL,
            purchase_price REAL NOT NULL DEFAULT 0,
            FOREIGN KEY (sale_id) REFERENCES sales(id) ON DELETE CASCADE,
            FOREIGN KEY (product_id) REFERENCES products(id)
        );

        CREATE TABLE IF NOT EXISTS invoices ( id INTEGER PRIMARY KEY AUTOINCREMENT, invoice_number TEXT NOT NULL UNIQUE, client_name TEXT, client_address TEXT, client_phone TEXT, client_ice TEXT, total_amount REAL NOT NULL, invoice_date TEXT NOT NULL DEFAULT (strftime('%Y-%m-%d', 'now', 'localtime')) );
        
        CREATE TABLE IF NOT EXISTS invoice_items (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            invoice_id INTEGER NOT NULL,
            description TEXT NOT NULL,
            quantity INTEGER NOT NULL,
            unit_price REAL NOT NULL,
            unit TEXT NOT NULL DEFAULT 'piece',
            line_total REAL NOT NULL,
            FOREIGN KEY (invoice_id) REFERENCES invoices(id) ON DELETE CASCADE
        );
        
        CREATE TABLE IF NOT EXISTS settings ( key TEXT PRIMARY KEY, value TEXT );
        CREATE TABLE IF NOT EXISTS credit_payments ( id INTEGER PRIMARY KEY AUTOINCREMENT, client_id INTEGER NOT NULL, user_id INTEGER NOT NULL, amount_paid REAL NOT NULL, payment_date TEXT NOT NULL DEFAULT (strftime('%Y-%m-%d %H:%M:%S', 'now', 'localtime')), note TEXT, FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE CASCADE, FOREIGN KEY (user_id) REFERENCES users(id) );
        CREATE TABLE IF NOT EXISTS stock_adjustments ( id INTEGER PRIMARY KEY AUTOINCREMENT, product_id INTEGER NOT NULL, user_id INTEGER NOT NULL, old_quantity INTEGER NOT NULL, new_quantity INTEGER NOT NULL, reason TEXT, adjustment_date TEXT NOT NULL DEFAULT (strftime('%Y-%m-%d %H:%M:%S', 'now', 'localtime')), FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE, FOREIGN KEY (user_id) REFERENCES users(id) );
    `);

    // Migration : Ajouter la colonne original_sale_id si elle n'existe pas
    try {
        const columns = db.prepare("PRAGMA table_info(sales)").all();
        const hasOriginalSaleId = columns.some(col => col.name === 'original_sale_id');
        if (!hasOriginalSaleId) {
            console.log('Migration: Ajout de la colonne original_sale_id à la table sales');
            db.exec('ALTER TABLE sales ADD COLUMN original_sale_id INTEGER REFERENCES sales(id) ON DELETE SET NULL');
        }
    } catch (error) {
        console.error('Erreur lors de la migration original_sale_id:', error);
    }

    // Migration : Ajouter la colonne payment_method si elle n'existe pas
    try {
        const columns = db.prepare("PRAGMA table_info(sales)").all();
        const hasPaymentMethod = columns.some(col => col.name === 'payment_method');
        if (!hasPaymentMethod) {
            console.log('Migration: Ajout de la colonne payment_method à la table sales');
            db.exec("ALTER TABLE sales ADD COLUMN payment_method TEXT NOT NULL DEFAULT 'cash' CHECK(payment_method IN ('cash', 'check', 'credit'))");
        }
    } catch (error) {
        console.error('Erreur lors de la migration payment_method:', error);
    }

    const ownerExists = db.prepare("SELECT id FROM users WHERE username = 'proprietaire'").get();
    if (!ownerExists) {
        const passwordHash = bcrypt.hashSync('admin', saltRounds);
        db.prepare("INSERT INTO users (username, password_hash, role) VALUES (?, ?, 'Propriétaire')").run('proprietaire', passwordHash);
    }
    db.prepare("INSERT INTO clients (id, name, ice) SELECT 1, 'Client de passage', null WHERE NOT EXISTS (SELECT 1 FROM clients WHERE id = 1)").run();
}

const addProduct = (product) => { 
    const stmt = db.prepare("INSERT INTO products (barcode, name, purchase_price, price_retail, price_wholesale, price_carton, pieces_per_carton, stock, alert_threshold, category, image_path) VALUES (@barcode, @name, @purchase_price, @price_retail, @price_wholesale, @price_carton, @pieces_per_carton, @stock, @alert_threshold, @category, @image_path)"); 
    const info = stmt.run({ ...product, barcode: product.barcode || null, purchase_price: product.purchase_price || 0, price_carton: product.price_carton || 0, pieces_per_carton: product.pieces_per_carton || 0, image_path: product.image_path || null }); 
    return { id: info.lastInsertRowid, ...product }; 
};

const updateProduct = (product) => {
    const stmt = db.prepare("UPDATE products SET barcode = @barcode, name = @name, purchase_price = @purchase_price, price_retail = @price_retail, price_wholesale = @price_wholesale, price_carton = @price_carton, pieces_per_carton = @pieces_per_carton, stock = @stock, alert_threshold = @alert_threshold, category = @category, image_path = @image_path WHERE id = @id");
    stmt.run({ ...product, barcode: product.barcode || null, purchase_price: product.purchase_price || 0, price_carton: product.price_carton || 0, pieces_per_carton: product.pieces_per_carton || 0, image_path: product.image_path || null });
    return true;
};

const updateProductThreshold = (id, threshold) => {
    const stmt = db.prepare("UPDATE products SET alert_threshold = ? WHERE id = ?");
    stmt.run(threshold, id);
    return true;
};

const deleteProduct = (id) => { db.prepare("DELETE FROM products WHERE id = ?").run(id); return true; };
const getAllProducts = (searchTerm = '') => db.prepare("SELECT * FROM products WHERE name LIKE ? OR barcode LIKE ? ORDER BY name ASC").all(`%${searchTerm}%`, `%${searchTerm}%`);
const getProductById = (id) => db.prepare("SELECT * FROM products WHERE id = ?").get(id);
const getCategories = () => db.prepare("SELECT DISTINCT category FROM products WHERE category IS NOT NULL AND category != '' ORDER BY category ASC").all().map(r => r.category);
const getLowStockProducts = () => db.prepare('SELECT name, stock, alert_threshold FROM products WHERE stock <= alert_threshold AND alert_threshold > 0').all();
const adjustStock = db.transaction((adjustments, reason, userId) => { const updateStmt = db.prepare('UPDATE products SET stock = ? WHERE id = ?'); const logStmt = db.prepare('INSERT INTO stock_adjustments (product_id, user_id, old_quantity, new_quantity, reason) VALUES (?, ?, ?, ?, ?)'); for (const adj of adjustments) { updateStmt.run(adj.newQuantity, adj.productId); logStmt.run(adj.productId, userId, adj.oldQuantity, adj.newQuantity, reason); } return { success: true }; });
const addClient = (client) => { const stmt = db.prepare("INSERT INTO clients (name, phone, address, ice) VALUES (@name, @phone, @address, @ice)"); const info = stmt.run({ name: client.name, phone: client.phone || null, address: client.address || null, ice: client.ice || null }); return { id: info.lastInsertRowid, ...client }; };
const updateClient = (client) => { db.prepare("UPDATE clients SET name = ?, phone = ?, address = ?, ice = ? WHERE id = ?").run(client.name, client.phone || null, client.address || null, client.ice || null, client.id); return true; };
const deleteClient = (id) => { if (id === 1) return false; db.prepare("DELETE FROM clients WHERE id = ?").run(id); return true; };
const getAllClients = (searchTerm = '') => db.prepare("SELECT * FROM clients WHERE name LIKE ? OR phone LIKE ? ORDER BY name ASC").all(`%${searchTerm}%`, `%${searchTerm}%`);
const getClientById = (id) => db.prepare("SELECT * FROM clients WHERE id = ?").get(id);

const processSale = db.transaction((saleData) => {
    const { clientId, userId, cart, total, amountPaidCash, credit, paymentMethod = 'cash' } = saleData;
    const saleStmt = db.prepare(`INSERT INTO sales (client_id, user_id, total_amount, amount_paid_cash, amount_paid_credit, payment_method) VALUES (?, ?, ?, ?, ?, ?)`);
    const saleResult = saleStmt.run(clientId, userId, total, amountPaidCash, credit, paymentMethod);
    const saleId = saleResult.lastInsertRowid;
    if (!saleId) throw new Error("La création de la vente a échoué.");
    const getProductInfoStmt = db.prepare('SELECT pieces_per_carton FROM products WHERE id = ?');
    const updateStockStmt = db.prepare('UPDATE products SET stock = stock - ? WHERE id = ?');
    const insertItemStmt = db.prepare(`INSERT INTO sale_items (sale_id, product_id, quantity, unit, unit_price, line_total, purchase_price) VALUES (?, ?, ?, ?, ?, ?, ?)`);
    for (const item of cart) {
        let stockToDeduct = item.quantity;
        if (item.unit === 'carton') {
            const productInfo = getProductInfoStmt.get(item.id);
            if (productInfo && productInfo.pieces_per_carton > 0) {
                stockToDeduct = item.quantity * productInfo.pieces_per_carton;
            } else {
                console.warn(`Produit ID ${item.id} vendu en carton sans 'pieces_per_carton' défini.`);
            }
        }
        insertItemStmt.run(saleId, item.id, item.quantity, item.unit || 'piece', item.price, item.quantity * item.price, item.purchase_price || 0);
        updateStockStmt.run(stockToDeduct, item.id);
    }
    if (credit > 0 && clientId !== 1) {
        db.prepare('UPDATE clients SET credit_balance = credit_balance + ? WHERE id = ?').run(credit, clientId);
    }
    return { success: true, saleId: saleId };
});

// MODIFIÉ : La fonction de recherche dans l'historique a été entièrement réécrite
const getSalesHistory = (filters = {}) => {
    let sql = `
        SELECT
            s.id as sale_id,
            s.sale_date,
            s.status,
            s.total_amount,
            s.amount_paid_cash,
            s.amount_paid_credit,
            s.payment_method,
            s.original_sale_id,
            p.name as product_name,
            si.quantity,
            si.unit,
            si.unit_price,
            si.line_total,
            c.name as client_name,
            u.username as user_name,
            CASE
                WHEN s.original_sale_id IS NOT NULL THEN 'CORRECTION'
                WHEN EXISTS(SELECT 1 FROM sales WHERE original_sale_id = s.id) THEN 'CORRECTED'
                ELSE s.status
            END as display_status
        FROM sale_items si
        JOIN sales s ON si.sale_id = s.id
        JOIN products p ON si.product_id = p.id
        LEFT JOIN clients c ON s.client_id = c.id
        JOIN users u ON s.user_id = u.id
        WHERE 1=1
    `;
    const params = [];

    if (filters.clientId) {
        sql += ' AND s.client_id = ?';
        params.push(filters.clientId);
    }
    if (filters.productId) {
        sql += ' AND si.product_id = ?';
        params.push(filters.productId);
    }
    if (filters.startDate) {
        sql += ' AND date(s.sale_date) >= ?';
        params.push(filters.startDate);
    }
    if (filters.endDate) {
        sql += ' AND date(s.sale_date) <= ?';
        params.push(filters.endDate);
    }
    
    sql += ' ORDER BY s.sale_date DESC';

    return db.prepare(sql).all(params);
};

const getHistoryForUser = (userId) => db.prepare(`SELECT s.id, s.sale_date, s.total_amount, s.status, c.name as client_name FROM sales s JOIN clients c ON s.client_id = c.id WHERE s.user_id = ? AND date(s.sale_date) = date('now', 'localtime') ORDER BY s.sale_date DESC`).all(userId);
const getSaleDetails = (saleId) => {
    const sale = db.prepare(`SELECT s.*, c.name as client_name FROM sales s JOIN clients c ON s.client_id = c.id WHERE s.id = ?`).get(saleId);
    if (sale) {
        sale.items = db.prepare(`SELECT si.*, p.name as product_name FROM sale_items si JOIN products p ON si.product_id = p.id WHERE si.sale_id = ?`).all(saleId);
        // Ajouter les informations de correction
        sale.corrections = db.prepare(`SELECT id, total_amount, sale_date, status FROM sales WHERE original_sale_id = ? ORDER BY sale_date DESC`).all(saleId);
        sale.original_sale = sale.original_sale_id ? db.prepare(`SELECT id, total_amount, sale_date, status FROM sales WHERE id = ?`).get(sale.original_sale_id) : null;
    }
    return sale;
};
const getLastSale = (userId) => db.prepare('SELECT id FROM sales WHERE user_id = ? ORDER BY id DESC LIMIT 1').get(userId);
const editSale = db.transaction((originalSaleId, newSaleData) => {
    const { clientId, userId, cart, total, amountPaidCash, credit, paymentMethod = 'cash' } = newSaleData;
    const originalSale = db.prepare('SELECT * FROM sales WHERE id = ?').get(originalSaleId);
    if (!originalSale) {
        throw new Error("Vente originale introuvable.");
    }

    // Récupérer les articles originaux et remettre le stock
    const originalItems = db.prepare('SELECT * FROM sale_items WHERE sale_id = ?').all(originalSaleId);
    const restockStmt = db.prepare('UPDATE products SET stock = stock + ? WHERE id = ?');
    const getProductInfoStmt = db.prepare('SELECT pieces_per_carton FROM products WHERE id = ?');

    for (const item of originalItems) {
        let stockToRestore = item.quantity;
        if (item.unit === 'carton') {
            const productInfo = getProductInfoStmt.get(item.product_id);
            if (productInfo && productInfo.pieces_per_carton > 0) {
                stockToRestore = item.quantity * productInfo.pieces_per_carton;
            }
        }
        restockStmt.run(stockToRestore, item.product_id);
    }

    // Annuler le crédit de la vente originale
    if (originalSale.amount_paid_credit > 0 && originalSale.client_id !== 1) {
        db.prepare('UPDATE clients SET credit_balance = credit_balance - ? WHERE id = ?').run(originalSale.amount_paid_credit, originalSale.client_id);
    }

    // Marquer la vente originale comme corrigée
    db.prepare("UPDATE sales SET status = 'CORRECTED' WHERE id = ?").run(originalSaleId);

    // Créer la nouvelle vente avec référence à l'originale
    const saleStmt = db.prepare(`INSERT INTO sales (client_id, user_id, total_amount, amount_paid_cash, amount_paid_credit, payment_method, original_sale_id) VALUES (?, ?, ?, ?, ?, ?, ?)`);
    const saleResult = saleStmt.run(clientId, userId, total, amountPaidCash, credit, paymentMethod, originalSaleId);
    const newSaleId = saleResult.lastInsertRowid;
    if (!newSaleId) {
        throw new Error("La création de la vente corrigée a échoué.");
    }

    // Ajouter les nouveaux articles et déduire le stock
    const updateStockStmt = db.prepare('UPDATE products SET stock = stock - ? WHERE id = ?');
    const insertItemStmt = db.prepare(`INSERT INTO sale_items (sale_id, product_id, quantity, unit, unit_price, line_total, purchase_price) VALUES (?, ?, ?, ?, ?, ?, ?)`);

    for (const item of cart) {
        let stockToDeduct = item.quantity;
        if (item.unit === 'carton') {
            const productInfo = getProductInfoStmt.get(item.id);
            if (productInfo && productInfo.pieces_per_carton > 0) {
                stockToDeduct = item.quantity * productInfo.pieces_per_carton;
            } else {
                console.warn(`Produit ID ${item.id} vendu en carton sans 'pieces_per_carton' défini.`);
            }
        }
        insertItemStmt.run(newSaleId, item.id, item.quantity, item.unit || 'piece', item.price, item.quantity * item.price, item.purchase_price || 0);
        updateStockStmt.run(stockToDeduct, item.id);
    }

    // Ajouter le nouveau crédit
    if (credit > 0 && clientId !== 1) {
        db.prepare('UPDATE clients SET credit_balance = credit_balance + ? WHERE id = ?').run(credit, clientId);
    }

    return { success: true, saleId: newSaleId };
});
const processReturn = db.transaction((originalSaleId, itemsToReturn, clientId) => { const originalSale = db.prepare('SELECT * FROM sales WHERE id = ?').get(originalSaleId); if (!originalSale) { throw new Error("Vente originale introuvable."); } let totalRefundAmount = 0; for (const item of itemsToReturn) { totalRefundAmount += item.quantity * item.unit_price; } if (totalRefundAmount <= 0) { throw new Error("Aucun article valide à retourner."); } const returnSaleStmt = db.prepare(`INSERT INTO sales (client_id, user_id, total_amount, amount_paid_cash, amount_paid_credit, status) VALUES (?, ?, ?, ?, ?, ?)`); const returnResult = returnSaleStmt.run(clientId, originalSale.user_id, -totalRefundAmount, 0, -totalRefundAmount, 'RETURNED'); const returnSaleId = returnResult.lastInsertRowid; const updateStockStmt = db.prepare('UPDATE products SET stock = stock + ? WHERE id = ?'); const insertReturnItemStmt = db.prepare(`INSERT INTO sale_items (sale_id, product_id, quantity, unit, unit_price, line_total, purchase_price) VALUES (?, ?, ?, ?, ?, ?, ?)`); for (const item of itemsToReturn) { const originalItem = db.prepare('SELECT purchase_price FROM sale_items WHERE sale_id = ? AND product_id = ?').get(originalSaleId, item.product_id); insertReturnItemStmt.run(returnSaleId, item.product_id, -item.quantity, item.unit || 'piece', item.unit_price, -item.quantity * item.unit_price, originalItem ? originalItem.purchase_price : 0); } if (clientId !== 1) { const updateCreditStmt = db.prepare('UPDATE clients SET credit_balance = credit_balance - ? WHERE id = ?'); updateCreditStmt.run(totalRefundAmount, clientId); } db.prepare(`UPDATE sales SET status = 'RETURNED' WHERE id = ?`).run(originalSaleId); return { success: true, returnSaleId }; });
const getDebtors = () => db.prepare(`SELECT id, name, phone, credit_balance FROM clients WHERE credit_balance > 0.01 ORDER BY name ASC`).all();
const getClientCredit = (clientId) => {
    const result = db.prepare(`SELECT credit_balance FROM clients WHERE id = ?`).get(clientId);
    return result ? result.credit_balance : 0;
};
const recordCreditPayment = db.transaction((paymentData) => { const { clientId, amount, userId } = paymentData; db.prepare('UPDATE clients SET credit_balance = credit_balance - ? WHERE id = ?').run(amount, clientId); db.prepare('INSERT INTO credit_payments (client_id, user_id, amount_paid, note) VALUES (?, ?, ?, ?)').run(clientId, userId, amount, 'Paiement de crédit'); return { success: true }; });
const addManualCredit = db.transaction((creditData) => { const { clientId, amount, note, userId } = creditData; db.prepare('UPDATE clients SET credit_balance = credit_balance + ? WHERE id = ?').run(amount, clientId); db.prepare('INSERT INTO credit_payments (client_id, user_id, amount_paid, note) VALUES (?, ?, ?, ?)').run(clientId, userId, -amount, note); return { success: true }; });
const createInvoice = db.transaction((invoiceData) => { const { invoice_number, invoice_date, client_name, client_address, client_phone, client_ice, total_amount, items } = invoiceData; const invoiceStmt = db.prepare(`INSERT INTO invoices (invoice_number, invoice_date, client_name, client_address, client_phone, client_ice, total_amount) VALUES (?, ?, ?, ?, ?, ?, ?)`); const result = invoiceStmt.run(invoice_number, invoice_date, client_name, client_address, client_phone, client_ice, total_amount); const invoiceId = result.lastInsertRowid; if (!invoiceId) { throw new Error("La création de la facture a échoué."); } const itemStmt = db.prepare(`INSERT INTO invoice_items (invoice_id, description, quantity, unit_price, unit, line_total) VALUES (?, ?, ?, ?, ?, ?)`); for (const item of items) { itemStmt.run(invoiceId, item.description, item.quantity, item.unit_price, item.unit || 'piece', item.line_total); } return { success: true, invoiceId }; });
const getInvoices = () => db.prepare(`SELECT id, invoice_number, client_name, total_amount, invoice_date FROM invoices ORDER BY id DESC`).all();
const getInvoiceDetails = (id) => { const invoice = db.prepare(`SELECT * FROM invoices WHERE id = ?`).get(id); if (invoice) { invoice.items = db.prepare(`SELECT * FROM invoice_items WHERE invoice_id = ?`).all(id); } return invoice; };
const getNextInvoiceNumber = () => { const year = new Date().getFullYear(); const prefix = `FACT-${year}-`; const lastInvoice = db.prepare(`SELECT invoice_number FROM invoices WHERE invoice_number LIKE ? ORDER BY id DESC LIMIT 1`).get(prefix + '%'); let next = 1; if (lastInvoice) { next = parseInt(lastInvoice.invoice_number.split('-')[2], 10) + 1; } return prefix + String(next).padStart(4, '0'); };
const saveSetting = (key, value) => { db.prepare('INSERT OR REPLACE INTO settings (key, value) VALUES (?, ?)' ).run(key, value); return true; };
const getSetting = (key) => { const result = db.prepare('SELECT value FROM settings WHERE key = ?').get(key); return result ? result.value : null; };
const getCompanyInfo = () => ({ name: getSetting('company_name'), address: getSetting('company_address'), phone: getSetting('company_phone'), ice: getSetting('company_ice'), email: getSetting('company_email'), website: getSetting('company_website') });
const saveCompanyInfo = (info) => { saveSetting('company_name', info.name); saveSetting('company_address', info.address); saveSetting('company_phone', info.phone); saveSetting('company_ice', info.ice); saveSetting('company_email', info.email); saveSetting('company_website', info.website); return { success: true }; };
const authenticateUser = (username, password) => { const user = db.prepare("SELECT * FROM users WHERE username = ?").get(username); if (user && bcrypt.compareSync(password, user.password_hash)) { const { password_hash, ...userWithoutHash } = user; return userWithoutHash; } return null; };
const getAllUsers = () => db.prepare("SELECT id, username, role FROM users WHERE role = 'Vendeur'").all();
const addUser = (username, password) => { db.prepare("INSERT INTO users (username, password_hash, role) VALUES (?, ?, 'Vendeur')").run(username, bcrypt.hashSync(password, saltRounds)); return true; };
const updateUserPassword = (id, password) => { db.prepare("UPDATE users SET password_hash = ? WHERE id = ?").run(bcrypt.hashSync(password, saltRounds), id); return true; };
const deleteUser = (id) => { db.prepare("DELETE FROM users WHERE id = ?").run(id); return true; };
const updateUserCredentials = (data) => {
    const { id, currentUsername, newUsername, currentPassword, newPassword } = data;
    const user = db.prepare("SELECT * FROM users WHERE id = ?").get(id);
    if (!user) { throw new Error("Utilisateur non trouvé."); }
    const isPasswordCorrect = bcrypt.compareSync(currentPassword, user.password_hash);
    if (!isPasswordCorrect) { throw new Error("Le mot de passe actuel est incorrect."); }
    const isUsernameChanged = newUsername && newUsername !== currentUsername;
    const isPasswordChanged = newPassword && newPassword.length > 0;
    if (!isUsernameChanged && !isPasswordChanged) { throw new Error("Aucune modification n'a été fournie."); }
    if (isUsernameChanged) {
        const existingUser = db.prepare("SELECT id FROM users WHERE username = ? AND id != ?").get(newUsername, id);
        if (existingUser) { throw new Error("Ce nom d'utilisateur est déjà utilisé par un autre compte."); }
    }
    const params = [];
    let sql = 'UPDATE users SET ';
    if (isUsernameChanged) {
        sql += 'username = ?';
        params.push(newUsername);
    }
    if (isPasswordChanged) {
        if (isUsernameChanged) sql += ', ';
        sql += 'password_hash = ?';
        const newPasswordHash = bcrypt.hashSync(newPassword, saltRounds);
        params.push(newPasswordHash);
    }
    sql += ' WHERE id = ?';
    params.push(id);
    db.prepare(sql).run(...params);
    return { success: true };
};
const getDashboardStats = ({ startDate, endDate }) => { const revenue = db.prepare(`SELECT COALESCE(SUM(total_amount), 0) as revenue FROM sales WHERE status = 'COMPLETED' AND date(sale_date) BETWEEN ? AND ?`).get(startDate, endDate).revenue; const credit_given = db.prepare(`SELECT COALESCE(SUM(amount_paid_credit), 0) as credit_given FROM sales WHERE status = 'COMPLETED' AND amount_paid_credit > 0 AND date(sale_date) BETWEEN ? AND ?`).get(startDate, endDate).credit_given; const total_cost = db.prepare(`SELECT COALESCE(SUM(si.quantity * si.purchase_price), 0) as total_cost FROM sale_items si JOIN sales s ON si.sale_id = s.id WHERE s.status = 'COMPLETED' AND date(s.sale_date) BETWEEN ? AND ?`).get(startDate, endDate).total_cost; return { revenue, credit_given, total_cost }; };

// Nouvelles fonctions pour les analytics produits - v2
const getTopProfitableProducts = ({ startDate, endDate, limit = 5 }) => {
    return db.prepare(`
        SELECT
            p.id,
            p.name,
            p.category,
            SUM(si.quantity) as total_quantity,
            SUM(si.line_total) as total_revenue,
            SUM(si.quantity * si.purchase_price) as total_cost,
            SUM(si.line_total - (si.quantity * si.purchase_price)) as total_profit,
            ROUND((SUM(si.line_total - (si.quantity * si.purchase_price)) / SUM(si.line_total)) * 100, 2) as profit_margin
        FROM sale_items si
        JOIN sales s ON si.sale_id = s.id
        JOIN products p ON si.product_id = p.id
        WHERE s.status = 'COMPLETED'
        AND date(s.sale_date) BETWEEN ? AND ?
        GROUP BY p.id, p.name, p.category
        HAVING total_profit > 0
        ORDER BY total_profit DESC
        LIMIT ?
    `).all(startDate, endDate, limit);
};

const getTopSellingProducts = ({ startDate, endDate, limit = 5 }) => {
    return db.prepare(`
        SELECT
            p.id,
            p.name,
            p.category,
            SUM(si.quantity) as total_quantity,
            SUM(si.line_total) as total_revenue,
            COUNT(DISTINCT s.id) as sales_count,
            ROUND(SUM(si.quantity) / COUNT(DISTINCT date(s.sale_date)), 2) as avg_daily_sales
        FROM sale_items si
        JOIN sales s ON si.sale_id = s.id
        JOIN products p ON si.product_id = p.id
        WHERE s.status = 'COMPLETED'
        AND date(s.sale_date) BETWEEN ? AND ?
        GROUP BY p.id, p.name, p.category
        ORDER BY total_quantity DESC
        LIMIT ?
    `).all(startDate, endDate, limit);
};

const getProductPerformanceOverview = ({ startDate, endDate }) => {
    return db.prepare(`
        SELECT
            p.id,
            p.name,
            p.category,
            p.stock,
            p.alert_threshold,
            SUM(si.quantity) as total_quantity,
            SUM(si.line_total) as total_revenue,
            SUM(si.quantity * si.purchase_price) as total_cost,
            SUM(si.line_total - (si.quantity * si.purchase_price)) as total_profit,
            ROUND((SUM(si.line_total - (si.quantity * si.purchase_price)) / SUM(si.line_total)) * 100, 2) as profit_margin,
            COUNT(DISTINCT s.id) as sales_frequency
        FROM sale_items si
        JOIN sales s ON si.sale_id = s.id
        JOIN products p ON si.product_id = p.id
        WHERE s.status = 'COMPLETED'
        AND date(s.sale_date) BETWEEN ? AND ?
        GROUP BY p.id, p.name, p.category, p.stock, p.alert_threshold
        HAVING total_quantity > 0
        ORDER BY total_profit DESC
    `).all(startDate, endDate);
};

const getProductInsights = ({ startDate, endDate }) => {
    const insights = [];

    // Produit en forte croissance (comparaison avec période précédente)
    const growthProducts = db.prepare(`
        WITH current_period AS (
            SELECT p.name, SUM(si.quantity) as current_sales
            FROM sale_items si
            JOIN sales s ON si.sale_id = s.id
            JOIN products p ON si.product_id = p.id
            WHERE s.status = 'COMPLETED' AND date(s.sale_date) BETWEEN ? AND ?
            GROUP BY p.id, p.name
        ),
        previous_period AS (
            SELECT p.name, SUM(si.quantity) as previous_sales
            FROM sale_items si
            JOIN sales s ON si.sale_id = s.id
            JOIN products p ON si.product_id = p.id
            WHERE s.status = 'COMPLETED'
            AND date(s.sale_date) BETWEEN date(?, '-' || (julianday(?) - julianday(?)) || ' days') AND date(?, '-1 day')
            GROUP BY p.id, p.name
        )
        SELECT
            cp.name,
            cp.current_sales,
            COALESCE(pp.previous_sales, 0) as previous_sales,
            ROUND(((cp.current_sales - COALESCE(pp.previous_sales, 0)) / COALESCE(pp.previous_sales, 1)) * 100, 1) as growth_rate
        FROM current_period cp
        LEFT JOIN previous_period pp ON cp.name = pp.name
        WHERE cp.current_sales > COALESCE(pp.previous_sales, 0) * 1.25
        ORDER BY growth_rate DESC
        LIMIT 1
    `).get(startDate, endDate, startDate, endDate, startDate, startDate);

    if (growthProducts) {
        insights.push({
            type: 'growth',
            icon: '📈',
            message: `${growthProducts.name} en forte croissance (+${growthProducts.growth_rate}%)`
        });
    }

    // Produits rentables avec stock faible
    const lowStockProfitable = db.prepare(`
        SELECT p.name, p.stock, p.alert_threshold,
               SUM(si.line_total - (si.quantity * si.purchase_price)) as profit
        FROM sale_items si
        JOIN sales s ON si.sale_id = s.id
        JOIN products p ON si.product_id = p.id
        WHERE s.status = 'COMPLETED'
        AND date(s.sale_date) BETWEEN ? AND ?
        AND p.stock <= p.alert_threshold
        AND p.alert_threshold > 0
        GROUP BY p.id, p.name, p.stock, p.alert_threshold
        HAVING profit > 0
        ORDER BY profit DESC
        LIMIT 1
    `).get(startDate, endDate);

    if (lowStockProfitable) {
        insights.push({
            type: 'warning',
            icon: '⚠️',
            message: `Stock faible sur produit rentable: ${lowStockProfitable.name}`
        });
    }

    // Opportunité d'augmentation de prix
    const pricingOpportunity = db.prepare(`
        SELECT p.name,
               ROUND((SUM(si.line_total - (si.quantity * si.purchase_price)) / SUM(si.line_total)) * 100, 1) as margin
        FROM sale_items si
        JOIN sales s ON si.sale_id = s.id
        JOIN products p ON si.product_id = p.id
        WHERE s.status = 'COMPLETED'
        AND date(s.sale_date) BETWEEN ? AND ?
        GROUP BY p.id, p.name
        HAVING SUM(si.quantity) > 10 AND margin > 50
        ORDER BY SUM(si.quantity) DESC
        LIMIT 1
    `).get(startDate, endDate);

    if (pricingOpportunity) {
        insights.push({
            type: 'opportunity',
            icon: '💡',
            message: `Opportunité: Augmenter prix ${pricingOpportunity.name} (marge ${pricingOpportunity.margin}%)`
        });
    }

    return insights;
};

module.exports = { initDatabase, productDB: { getAll: getAllProducts, getById: getProductById, add: addProduct, update: updateProduct, delete: deleteProduct, getCategories, getLowStock: getLowStockProducts, adjustStock, updateThreshold: updateProductThreshold }, clientDB: { getAll: getAllClients, getById: getClientById, add: addClient, update: updateClient, delete: deleteClient }, salesDB: { process: processSale, getHistory: getSalesHistory, getHistoryForUser, getDetails: getSaleDetails, edit: editSale, getLast: getLastSale, processReturn }, creditsDB: { getDebtors, getClientCredit, recordPayment: recordCreditPayment, addManual: addManualCredit }, invoicesDB: { create: createInvoice, getAll: getInvoices, getDetails: getInvoiceDetails, getNextNumber: getNextInvoiceNumber }, settingsDB: { save: saveSetting, get: getSetting, getCompanyInfo, saveCompanyInfo }, usersDB: { authenticateUser, getAll: getAllUsers, add: addUser, updatePassword: updateUserPassword, delete: deleteUser, updateCredentials: updateUserCredentials }, dashboardDB: { getStats: getDashboardStats, getTopProfitable: getTopProfitableProducts, getTopSelling: getTopSellingProducts, getPerformanceOverview: getProductPerformanceOverview, getInsights: getProductInsights } };