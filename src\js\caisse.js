// caisse.js - Basé sur la version stable fournie, avec traductions et corrections de bugs.

function debounce(func, delay = 300) {
    let timeoutId;
    return (...args) => {
        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => {
            func.apply(this, args);
        }, delay);
    };
}

// Variables pour éviter les conflits de rendu et gérer l'état
let isRendering = false;
let isProcessing = false;
let isUserTyping = false;

// Fonction utilitaire pour gérer l'état des inputs
function setInputsState(disabled) {
    const inputs = document.querySelectorAll('input, button');
    inputs.forEach(input => {
        if (disabled) {
            input.dataset.wasDisabled = input.disabled;
            input.disabled = true;
        } else {
            input.disabled = input.dataset.wasDisabled === 'true';
        }
    });
}

document.addEventListener('DOMContentLoaded', async () => {
    // --- Initialisation premium de la page ---
    await initPagePremium('caisse', async () => {
        // Initialisation spécifique à la page caisse
        await initCaissePage();
    });
});

async function initCaissePage() {
    // --- Récupération de la fonction de traduction ---
    const t = window.i18n.t;

    // --- Vérification des API ---
    if (!window.api || !window.api.products || !window.api.clients || !window.api.sales || !window.api.session) {
        document.body.innerHTML = "<h1 class='text-red-500 text-center p-8'>ERREUR CRITIQUE: Une API nécessaire est manquante.</h1>";
        return;
    }

    // --- État de l'application (inchangé) ---
    let cart = [], allProducts = [], categories = [], selectedCategory = 'all', selectedClientId = 1, editMode = false,
        originalSaleId = null, countdownInterval = null, barcodeBuffer = '', barcodeTimer = null;

    // Variables supplémentaires pour le scanner code-barres
    let lastKeyTime = 0;
    let isScanning = false;
    let scannerTimeout = null;

    // Variables pour l'impression
    let lastSaleData = null;

    // --- Variables pour le nouveau workflow de modification ---
    let editWorkflowStep = 'products'; // 'products' | 'payment'
    let originalSaleData = null; // Stockage des données originales de la vente

    // --- Fonctions du Scanner Code-Barres ---

    /**
     * Recherche un produit par code-barres
     */
    function findProductByBarcode(barcode) {
        if (!barcode || barcode.trim() === '') return null;

        // Nettoyer le code-barres (supprimer les espaces et caractères spéciaux)
        const cleanBarcode = barcode.trim().replace(/[^\w\-]/g, '');

        return allProducts.find(product => {
            if (!product.barcode) return false;
            const productBarcode = product.barcode.trim().replace(/[^\w\-]/g, '');
            return productBarcode === cleanBarcode;
        });
    }

    /**
     * Traite le scan d'un code-barres
     */
    async function processBarcodeInput(barcode) {
        if (!barcode || barcode.trim() === '') return;

        const barcodeInput = document.getElementById('barcodeInput');
        const scannerStatus = document.getElementById('scannerStatus');
        const scannerFeedback = document.getElementById('scannerFeedback');
        const scannerMessage = document.getElementById('scannerMessage');

        try {
            // Mettre à jour le statut
            updateScannerStatus('scanning');

            // Rechercher le produit
            const product = findProductByBarcode(barcode);

            if (product) {
                // Produit trouvé - l'ajouter au panier
                addProductToCart(product.id);

                // Feedback positif
                showScannerFeedback('success', t('product_found_by_barcode') + ': ' + product.name);

                // Vider le champ
                if (barcodeInput) barcodeInput.value = '';

                // Mettre à jour le statut
                updateScannerStatus('ready');

            } else {
                // Produit non trouvé
                showScannerFeedback('error', t('product_not_found_by_barcode'));
                updateScannerStatus('ready');
            }

        } catch (error) {
            console.error('Erreur lors du traitement du code-barres:', error);
            showScannerFeedback('error', t('barcode_scan_error'));
            updateScannerStatus('ready');
        }
    }

    /**
     * Met à jour le statut du scanner
     */
    function updateScannerStatus(status) {
        const scannerStatus = document.getElementById('scannerStatus');
        if (!scannerStatus) return;

        const statusDot = scannerStatus.querySelector('.w-3.h-3');
        const statusText = scannerStatus.querySelector('span');

        if (statusDot && statusText) {
            switch (status) {
                case 'ready':
                    statusDot.className = 'w-3 h-3 bg-green-500 rounded-full animate-pulse';
                    statusText.textContent = t('scanner_ready');
                    break;
                case 'scanning':
                    statusDot.className = 'w-3 h-3 bg-blue-500 rounded-full animate-spin';
                    statusText.textContent = t('scanner_scanning');
                    break;
                case 'error':
                    statusDot.className = 'w-3 h-3 bg-red-500 rounded-full';
                    statusText.textContent = 'Erreur';
                    break;
            }
        }
    }

    /**
     * Affiche un feedback du scanner
     */
    function showScannerFeedback(type, message) {
        const scannerFeedback = document.getElementById('scannerFeedback');
        const scannerMessage = document.getElementById('scannerMessage');

        if (!scannerFeedback || !scannerMessage) return;

        // Définir les couleurs selon le type
        let bgColor, textColor, iconPath;

        switch (type) {
            case 'success':
                bgColor = 'bg-green-100 dark:bg-green-900/30';
                textColor = 'text-green-800 dark:text-green-200';
                iconPath = 'M5 13l4 4L19 7'; // Checkmark
                break;
            case 'error':
                bgColor = 'bg-red-100 dark:bg-red-900/30';
                textColor = 'text-red-800 dark:text-red-200';
                iconPath = 'M6 18L18 6M6 6l12 12'; // X
                break;
            default:
                bgColor = 'bg-blue-100 dark:bg-blue-900/30';
                textColor = 'text-blue-800 dark:text-blue-200';
                iconPath = 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'; // Info
        }

        // Mettre à jour le contenu
        scannerFeedback.className = `mt-2 text-sm ${bgColor} ${textColor} rounded-lg`;
        scannerFeedback.innerHTML = `
            <div class="flex items-center gap-2 p-2">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="${iconPath}"></path>
                </svg>
                <span>${message}</span>
            </div>
        `;

        // Afficher le feedback
        scannerFeedback.classList.remove('hidden');

        // Masquer après 3 secondes
        setTimeout(() => {
            if (scannerFeedback) {
                scannerFeedback.classList.add('hidden');
            }
        }, 3000);
    }

    /**
     * Détecte si l'entrée provient d'un scanner (basé sur la vitesse de frappe)
     */
    function detectBarcodeScanner(event) {
        const currentTime = Date.now();
        const timeDiff = currentTime - lastKeyTime;
        lastKeyTime = currentTime;

        // Si l'intervalle entre les touches est très court (< 50ms), c'est probablement un scanner
        return timeDiff < 50 && timeDiff > 0;
    }

    // --- Fonctions d'Impression ---

    /**
     * Récupère les paramètres de la société depuis la base de données
     */
    async function getCompanySettings() {
        try {
            if (window.api && window.api.settings) {
                const settings = await window.api.settings.getCompanyInfo();
                return {
                    name: settings.name || settings.company_name || 'MAGASIN GÉNÉRAL',
                    address: settings.address || settings.company_address || '123 Avenue Mohammed V, Casablanca',
                    phone: settings.phone || settings.company_phone || '+*********** 456',
                    ice: settings.ice || settings.company_ice || '001234567890123',
                    email: settings.email || settings.company_email || '',
                    website: settings.website || settings.company_website || ''
                };
            }
        } catch (error) {
            console.error('Erreur lors de la récupération des paramètres société:', error);
        }

        // Fallback par défaut
        return {
            name: 'MAGASIN GÉNÉRAL',
            address: '123 Avenue Mohammed V, Casablanca',
            phone: '+*********** 456',
            ice: '001234567890123',
            email: '',
            website: ''
        };
    }

    /**
     * Récupère les informations du client sélectionné
     */
    async function getSelectedClientInfo() {
        try {
            if (selectedClientId && selectedClientId !== 1 && window.api && window.api.clients) {
                const client = await window.api.clients.getById(selectedClientId);
                return {
                    id: client.id,
                    name: client.name || 'Client',
                    phone: client.phone || '',
                    address: client.address || '',
                    credit: client.credit || 0
                };
            }
        } catch (error) {
            console.error('Erreur lors de la récupération des infos client:', error);
        }

        // Client par défaut
        return {
            id: 1,
            name: 'Client Passager',
            phone: '',
            address: '',
            credit: 0
        };
    }

    /**
     * Prépare les données de vente pour l'impression
     */
    async function prepareSaleDataForPrint(originalSaleData) {
        try {
            // Récupérer les paramètres de la société
            const companySettings = await getCompanySettings();

            // Récupérer les informations du client
            const clientInfo = await getSelectedClientInfo();

            // Récupérer le nom du vendeur (utilisateur connecté)
            let sellerName = 'Vendeur';
            try {
                if (window.api && window.api.auth) {
                    const currentUser = await window.api.auth.getCurrentUser();
                    sellerName = currentUser?.username || currentUser?.name || 'Vendeur';
                }
            } catch (error) {
                console.log('Impossible de récupérer le vendeur connecté');
            }

            // Calculer l'avance pour les paiements crédit
            let advanceAmount = 0;
            if (originalSaleData?.method === 'credit' || originalSaleData?.paymentMethod === 'credit') {
                // Récupérer l'avance depuis différentes sources possibles
                advanceAmount = originalSaleData?.advanceAmount ||
                              originalSaleData?.advance ||
                              originalSaleData?.amountPaidCash ||
                              originalSaleData?.amountPaid ||
                              parseFloat(document.getElementById('amountPaidInput')?.value) || 0;

                console.log('DEBUG AVANCE - originalSaleData:', originalSaleData);
                console.log('DEBUG AVANCE - advanceAmount calculé:', advanceAmount);
                console.log('DEBUG AVANCE - amountPaidInput value:', document.getElementById('amountPaidInput')?.value);
            }

            const printData = {
                // Données des produits
                items: cart.map(item => ({
                    name: item.name || 'Produit',
                    quantity: item.quantity || 1,
                    price: item.price || 0,
                    total: (item.quantity || 1) * (item.price || 0)
                })),

                // Données de paiement
                paymentMethod: originalSaleData?.method || originalSaleData?.paymentMethod || 'cash',
                amountPaid: originalSaleData?.amountPaid || originalSaleData?.amount || originalSaleData?.amountPaidCash || 0,
                advanceAmount: advanceAmount,
                checkNumber: originalSaleData?.checkNumber || originalSaleData?.check || null,
                discount: 0,

                // Données de la société (dynamiques)
                company: companySettings,

                // Données du vendeur (dynamiques)
                sellerName: sellerName,

                // Données du client (dynamiques)
                customer: clientInfo,
                customerName: clientInfo.name,

                // Métadonnées
                timestamp: new Date()
            };

            return printData;
        } catch (error) {
            console.error('Erreur dans prepareSaleDataForPrint:', error);
            // Retourner des données par défaut en cas d'erreur
            return {
                items: [{ name: 'Erreur', quantity: 1, price: 0, total: 0 }],
                paymentMethod: 'cash',
                amountPaid: 0,
                advanceAmount: 0,
                checkNumber: null,
                discount: 0,
                company: {
                    name: 'MAGASIN GÉNÉRAL',
                    address: '123 Avenue Mohammed V, Casablanca',
                    phone: '+*********** 456',
                    ice: '001234567890123'
                },
                sellerName: 'Vendeur',
                customer: { name: 'Client Passager' },
                customerName: 'Client Passager',
                timestamp: new Date()
            };
        }
    }

    /**
     * Récupère le nom du client sélectionné
     */
    function getClientName() {
        const clientDisplay = document.getElementById('selectedClientDisplay');
        return clientDisplay ? clientDisplay.textContent.trim() : null;
    }

    /**
     * Affiche le bouton d'impression après une vente réussie
     */
    function showPrintSection(saleData) {
        try {

            lastSaleData = saleData;

            // Stocker les données dans le ticket printer
            if (window.ticketPrinter) {
                window.ticketPrinter.setSaleData(saleData);
            } else {
                console.error('TicketPrinter non disponible');
            }

            // Afficher la section d'impression
            const printSection = document.getElementById('print-section');
            if (printSection) {
                printSection.classList.remove('hidden');

            } else {
                console.error('Élément print-section non trouvé');
            }
        } catch (error) {
            console.error('Erreur dans showPrintSection:', error);
        }
    }

    /**
     * Masque le bouton d'impression
     */
    function hidePrintSection() {
        const printSection = document.getElementById('print-section');
        if (printSection) {
            printSection.classList.add('hidden');
        }
        lastSaleData = null;
    }

    /**
     * Ouvre le modal de choix d'impression
     */
    function openPrintModal() {
        const modal = document.getElementById('printModal');
        if (modal) {
            modal.classList.remove('hidden');
            modal.style.display = 'flex';
        }
    }

    /**
     * Ferme le modal de choix d'impression
     */
    function closePrintModal() {
        const modal = document.getElementById('printModal');
        if (modal) {
            modal.classList.add('hidden');
            modal.style.display = 'none';
        }
    }

    // --- Éléments du DOM (inchangés) ---
    const productSearchInput = document.getElementById('productSearch');
    const categoryFiltersDiv = document.getElementById('category-filters');
    const productGridDiv = document.getElementById('product-grid');
    const clientSearchInput = document.getElementById('clientSearchInput');
    const clientSearchResultsDiv = document.getElementById('clientSearchResults');
    const selectedClientDisplay = document.getElementById('selectedClientDisplay');
    const selectedClientContainer = document.getElementById('selectedClientContainer');
    const clientCreditBadge = document.getElementById('clientCreditBadge');
    const cartItemsTbody = document.getElementById('cart-items');
    const totalAmountSpan = document.getElementById('total-amount');
    const amountPaidInput = document.getElementById('amount-paid');
    const setTotalBtn = document.getElementById('set-total-btn');
    const creditInfoP = document.getElementById('credit-info');
    const changeInfoP = document.getElementById('change-info');
    const cancelSaleBtn = document.getElementById('cancel-sale-btn');
    const cashPaymentBtn = document.getElementById('cash-payment-btn');
    const creditPaymentBtn = document.getElementById('credit-payment-btn');

    // Nouveaux éléments pour le workflow de paiement
    const validatePaymentBtn = document.getElementById('validate-payment-btn');
    const checkPaymentBtn = document.getElementById('check-payment-btn');
    const confirmPartialBtn = document.getElementById('confirm-partial-btn');
    const backToStep1Btn = document.getElementById('back-to-step1-btn');
    const backToStep2Btn = document.getElementById('back-to-step2-btn');
    const paymentStep1 = document.getElementById('payment-step-1');
    const paymentStep2 = document.getElementById('payment-step-2');
    const paymentStep3 = document.getElementById('payment-step-3');
    const totalDisplay = document.getElementById('total-display');
    const creditDisplay = document.getElementById('credit-display');
    const lastSalePanel = document.getElementById('lastSalePanel');
    const lastSaleIdSpan = document.getElementById('lastSaleId');
    const countdownSpan = document.getElementById('countdownSpan');
    const editSaleBtn = document.getElementById('editSaleBtn');
    const quickAddClientBtn = document.getElementById('quickAddClientBtn');
    const addClientModal = document.getElementById('addClientModal');
    const addClientForm = document.getElementById('addClientForm');
    const cancelAddClientBtn = document.getElementById('cancelAddClientBtn');

    // --- Fonctions utilitaires ---

    function showNotification(message, type = 'info') {
        // Créer une notification non-bloquante
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transition-all duration-300 ${
            type === 'success' ? 'bg-green-500 text-white' :
            type === 'error' ? 'bg-red-500 text-white' :
            'bg-blue-500 text-white'
        }`;
        notification.textContent = message;

        // Style initial pour l'animation
        notification.style.transform = 'translateX(100%)';
        notification.style.opacity = '0';

        document.body.appendChild(notification);

        // Animation d'entrée
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
            notification.style.opacity = '1';
        }, 10);

        // Suppression automatique après 3 secondes
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            notification.style.opacity = '0';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }

    // --- Fonctions (votre logique originale avec traductions) ---

    function renderCart() {
        if (!cartItemsTbody) return;
        cartItemsTbody.innerHTML = '';
        if (cart.length === 0) {
            const tr = document.createElement('tr');
            tr.innerHTML = `<td colspan="5" class="text-center py-8 text-gray-500">${t('cart_is_empty')}</td>`;
            cartItemsTbody.appendChild(tr);
        } else {
            cart.forEach(item => {
                // Validation défensive pour éviter les erreurs
                const safeItem = {
                    id: item.id || 0,
                    name: item.name || 'Produit inconnu',
                    price: parseFloat(item.price) || 0,
                    quantity: parseInt(item.quantity) || 1,
                    unit: item.unit || 'retail',
                    price_retail: parseFloat(item.price_retail) || 0,
                    price_wholesale: parseFloat(item.price_wholesale) || 0,
                    price_carton: parseFloat(item.price_carton) || 0,
                    pieces_per_carton: parseInt(item.pieces_per_carton) || 0,
                    stock: parseInt(item.stock) || 999
                };

                const tr = document.createElement('tr');
                tr.dataset.productId = safeItem.id;
                const retailBtnClass = safeItem.unit === 'retail' ? 'bg-blue-600 text-white' : 'bg-gray-200 dark:bg-gray-600';
                const wholesaleBtnClass = safeItem.unit === 'wholesale' ? 'bg-blue-600 text-white' : 'bg-gray-200 dark:bg-gray-600';
                const cartonBtnClass = safeItem.unit === 'carton' ? 'bg-blue-600 text-white' : 'bg-gray-200 dark:bg-gray-600';
                const isCartonDisabled = safeItem.pieces_per_carton === 0;
                tr.innerHTML = `<td class="px-2 py-2 align-top"><p class="font-semibold text-sm truncate">${safeItem.name}</p><div class="flex items-center gap-1 mt-1"><span class="text-xs mr-1">Tarif:</span><button class="set-price-btn text-xs px-2 py-0.5 rounded ${retailBtnClass}" data-price-type="retail" data-product-id="${safeItem.id}" title="Prix Détail">D</button><button class="set-price-btn text-xs px-2 py-0.5 rounded ${wholesaleBtnClass}" data-price-type="wholesale" data-product-id="${safeItem.id}" title="Prix Gros">G</button><button class="set-price-btn text-xs px-2 py-0.5 rounded ${cartonBtnClass}" data-price-type="carton" data-product-id="${safeItem.id}" title="Prix Carton" ${isCartonDisabled ? 'disabled style="opacity:0.5; cursor:not-allowed;"' : ''}>C</button></div></td><td class="px-2 py-2 align-top"><input type="number" class="quantity-input w-16 text-center font-bold border rounded dark:bg-gray-700 dark:border-gray-600" value="${safeItem.quantity}" min="1" max="${safeItem.stock}"></td><td class="px-2 py-2 align-top"><input type="number" step="0.01" class="price-input w-24 text-center font-bold border rounded dark:bg-gray-700 dark:border-gray-600" value="${safeItem.price.toFixed(2)}"></td><td class="line-total py-2 px-4 text-right font-bold whitespace-nowrap align-top">${(safeItem.quantity * safeItem.price).toFixed(2)}</td><td class="px-2 py-2 align-top"><button class="text-red-500 hover:text-red-700 remove-item-btn font-bold">X</button></td>`;
                cartItemsTbody.appendChild(tr);

                // Protéger les nouveaux inputs créés dynamiquement
                const quantityInput = tr.querySelector('.quantity-input');
                const priceInput = tr.querySelector('.price-input');
                protectInput(quantityInput);
                protectInput(priceInput);
            });
        }
        updateTotals();
        updatePaymentButtonsVisibility();
    }
    
    function addProductToCart(productId) {
        if (cart.length === 0 && !editMode) {
            hideLastSalePanel();
            hidePrintSection(); // Masquer le bouton d'impression quand on commence une nouvelle vente
        }
        const product = allProducts.find(p => p.id === productId);
        if (!product || product.stock <= 0) return;
        const unitToAdd = 'retail';
        let existingItem = cart.find(item => item.id === productId && item.unit === unitToAdd);
        if (existingItem) {
            if (existingItem.quantity < product.stock) {
                existingItem.quantity++;
            } else {
                showNotification(t('stock_max_reached'), 'warning');
            }
        } else {
            cart.push({ id: product.id, name: product.name, quantity: 1, unit: 'retail', price: product.price_retail, price_retail: product.price_retail, price_wholesale: product.price_wholesale, price_carton: product.price_carton, pieces_per_carton: product.pieces_per_carton, stock: product.stock });
        }
        renderCart();
    }

    function processBarcode(barcode) { if (!barcode || barcode.length <= 3) return; const product = allProducts.find(p => p.barcode === barcode.trim()); if (product) { addProductToCart(product.id); } else { console.log(`Code-barres non trouvé : ${barcode}`); } }
    function handleKeyDown(e) {
        const activeElement = document.activeElement;
        const isTypingInInput = activeElement && (
            activeElement.tagName === 'INPUT' ||
            activeElement.tagName === 'TEXTAREA' ||
            activeElement.tagName === 'SELECT' ||
            activeElement.isContentEditable ||
            activeElement.classList.contains('quantity-input') ||
            activeElement.classList.contains('price-input')
        );

        // Marquer que l'utilisateur tape si c'est dans un input
        if (isTypingInInput) {
            isUserTyping = true;
            // Arrêter le marquage après un délai
            clearTimeout(window.typingTimeout);
            window.typingTimeout = setTimeout(() => {
                isUserTyping = false;
            }, 500);
        }

        // Vérifier si une modal est ouverte
        const isModalOpen = addClientModal && !addClientModal.classList.contains('hidden');

        // Ne pas traiter les codes-barres si on tape dans un champ ou si une modal est ouverte
        // Aussi ignorer si l'utilisateur maintient Ctrl, Alt, ou Meta (pour les raccourcis)
        if (isTypingInInput || isModalOpen || e.ctrlKey || e.altKey || e.metaKey || isUserTyping) {
            return;
        }

        // Ignorer complètement si l'élément actif a un type spécifique
        if (activeElement && activeElement.type &&
            ['text', 'number', 'email', 'tel', 'password', 'search'].includes(activeElement.type)) {
            return;
        }

        // Raccourcis clavier pour le workflow de paiement
        if (e.key === 'F1' && !paymentStep1.classList.contains('hidden')) {
            e.preventDefault();
            if (cart.length > 0) showPaymentStep2();
            return;
        }

        if (e.key === 'F1' && !paymentStep2.classList.contains('hidden')) {
            e.preventDefault();
            processPayment('cash');
            return;
        }

        if (e.key === 'F2' && !paymentStep2.classList.contains('hidden')) {
            e.preventDefault();
            processPayment('check');
            return;
        }

        if (e.key === 'F3' && !paymentStep2.classList.contains('hidden')) {
            e.preventDefault();
            if (selectedClientId === 1) {
                showNotification(t('credit_for_default_client_error'), 'error');
                return;
            }
            showPaymentStep3();
            return;
        }

        if (e.key === 'Escape') {
            e.preventDefault();
            if (!paymentStep3.classList.contains('hidden')) {
                showPaymentStep2();
            } else if (!paymentStep2.classList.contains('hidden')) {
                showPaymentStep1();
            }
            return;
        }

        if (e.key === 'Enter') {
            e.preventDefault();
            processBarcode(barcodeBuffer);
            barcodeBuffer = '';
            return;
        }

        // Ignorer les touches spéciales et de navigation
        if (e.key.length > 1 || e.key === ' ') return;

        barcodeBuffer += e.key;
        clearTimeout(barcodeTimer);
        barcodeTimer = setTimeout(() => {
            barcodeBuffer = '';
        }, 100);
    }
    function handlePaste(e) {
        const activeElement = document.activeElement;
        const isTypingInInput = activeElement && (
            activeElement.tagName === 'INPUT' ||
            activeElement.tagName === 'TEXTAREA' ||
            activeElement.tagName === 'SELECT' ||
            activeElement.isContentEditable ||
            activeElement.classList.contains('quantity-input') ||
            activeElement.classList.contains('price-input')
        );
        const isModalOpen = addClientModal && !addClientModal.classList.contains('hidden');

        // Ne pas intercepter le paste si l'utilisateur tape dans un champ
        if (isTypingInInput || isModalOpen) return;

        // Ignorer complètement si l'élément actif a un type spécifique
        if (activeElement && activeElement.type &&
            ['text', 'number', 'email', 'tel', 'password', 'search'].includes(activeElement.type)) {
            return;
        }

        e.preventDefault();
        const pastedText = e.clipboardData ? e.clipboardData.getData('text') : '';
        if (pastedText) {
            processBarcode(pastedText);
        }
    }
    function openAddClientModal() {
        if (addClientModal) {
            addClientModal.classList.replace('hidden', 'flex');
            // Pas de focus automatique pour éviter les conflits avec les événements clavier
        }
    }
    function closeAddClientModal() {
        if (addClientModal) {
            addClientModal.classList.replace('flex', 'hidden');
            addClientForm.reset();
            // Pas de focus automatique pour éviter les conflits
        }
    }

    async function initPage() {
        if (typeof initializePage === 'function') await initializePage('caisse');
        [categories, allProducts] = await Promise.all([ window.api.products.getCategories(), window.api.products.getAll() ]);

        renderCategories();
        await renderProducts();

        // Vérifier s'il y a des données de modification depuis l'historique
        const editSaleData = localStorage.getItem('editSaleData');
        if (editSaleData) {
            try {
                const data = JSON.parse(editSaleData);
                if (data.isEdit && data.saleId && data.items) {
                    // Entrer en mode modification
                    editMode = true;
                    originalSaleId = data.saleId;

                    // Sélectionner le client
                    if (data.clientId) {
                        selectedClientId = data.clientId;
                        if (clientSearchInput) {
                            clientSearchInput.value = data.clientName || '';
                        }
                    }

                    // Charger les articles dans le panier avec données complètes
                    cart = data.items.map(item => {
                        // Trouver le produit complet pour récupérer toutes les informations
                        const fullProduct = allProducts.find(p => p.id === item.product_id);

                        if (fullProduct) {
                            return {
                                id: item.product_id,
                                name: item.product_name,
                                price: item.unit_price || 0,
                                quantity: item.quantity || 1,
                                unit: item.unit || 'retail',
                                total: item.line_total || 0,
                                price_retail: fullProduct.price_retail || 0,
                                price_wholesale: fullProduct.price_wholesale || 0,
                                price_carton: fullProduct.price_carton || 0,
                                pieces_per_carton: fullProduct.pieces_per_carton || 0,
                                stock: fullProduct.stock || 0
                            };
                        } else {
                            // Fallback si le produit n'est pas trouvé
                            return {
                                id: item.product_id,
                                name: item.product_name,
                                price: item.unit_price || 0,
                                quantity: item.quantity || 1,
                                unit: item.unit || 'retail',
                                total: item.line_total || 0,
                                price_retail: item.unit_price || 0,
                                price_wholesale: item.unit_price || 0,
                                price_carton: item.unit_price || 0,
                                pieces_per_carton: 0,
                                stock: 999
                            };
                        }
                    });

                    // Afficher un message d'information
                    showNotification(`Mode modification activé pour la vente #${data.saleId}`, 'info', 5000);

                    // Mettre à jour l'interface
                    if (editSaleBtn) {
                        editSaleBtn.textContent = `Modifier Vente #${data.saleId}`;
                        editSaleBtn.classList.remove('hidden');
                    }
                }

                // Nettoyer les données du localStorage
                localStorage.removeItem('editSaleData');
            } catch (error) {
                console.error('Erreur lors du chargement des données de modification:', error);
                localStorage.removeItem('editSaleData');
            }
        }

        renderCart();

        // S'assurer que les boutons de paiement sont dans le bon état au démarrage
        updatePaymentButtonsVisibility();

        // Mettre à jour l'affichage du client par défaut
        await updateClientDisplay(selectedClientId);

        // Pas de focus automatique pour éviter les conflits avec les événements clavier

        // Nettoyer les anciens événements avant d'en ajouter de nouveaux
        document.removeEventListener('keydown', handleKeyDown);
        document.removeEventListener('paste', handlePaste);

        document.addEventListener('keydown', handleKeyDown);
        document.addEventListener('paste', handlePaste);
    }
    
    function updateTotals() {
        // Utiliser requestAnimationFrame pour éviter le blocage
        requestAnimationFrame(() => {
            const total = cart.reduce((sum, item) => {
                const quantity = parseFloat(item.quantity) || 0;
                const price = parseFloat(item.price) || 0;
                return sum + (quantity * price);
            }, 0);
            if (totalAmountSpan) totalAmountSpan.textContent = total.toFixed(2);

            // Ne plus afficher automatiquement crédit/rendu ici
            // Ces informations seront gérées dans updatePartialPaymentDisplay()
        });
    }

    // === NOUVELLES FONCTIONS POUR LE WORKFLOW DE PAIEMENT ===

    /**
     * Gère l'affichage des boutons de paiement selon l'état du panier
     */
    function updatePaymentButtonsVisibility() {
        const hasItems = cart.length > 0;

        if (paymentStep1) {
            if (hasItems) {
                paymentStep1.classList.remove('hidden');
            } else {
                paymentStep1.classList.add('hidden');
                // Si on masque l'étape 1, masquer aussi les autres étapes
                if (paymentStep2) paymentStep2.classList.add('hidden');
                if (paymentStep3) paymentStep3.classList.add('hidden');
            }
        }
    }

    /**
     * Affiche l'étape 1 (boutons principaux)
     */
    function showPaymentStep1() {
        if (paymentStep1) paymentStep1.classList.remove('hidden');
        if (paymentStep2) paymentStep2.classList.add('hidden');
        if (paymentStep3) paymentStep3.classList.add('hidden');
    }

    /**
     * Affiche l'étape 2 (choix du type de paiement)
     */
    function showPaymentStep2() {
        if (paymentStep1) paymentStep1.classList.add('hidden');
        if (paymentStep2) paymentStep2.classList.remove('hidden');
        if (paymentStep3) paymentStep3.classList.add('hidden');
    }

    /**
     * Affiche l'étape 3 (détails pour crédit/partiel)
     */
    function showPaymentStep3() {
        if (paymentStep1) paymentStep1.classList.add('hidden');
        if (paymentStep2) paymentStep2.classList.add('hidden');
        if (paymentStep3) paymentStep3.classList.remove('hidden');

        // Mettre à jour les affichages
        updatePartialPaymentDisplay();
    }

    /**
     * Met à jour l'affichage pour le paiement partiel
     */
    function updatePartialPaymentDisplay() {
        const total = parseFloat(totalAmountSpan?.textContent) || 0;
        const amountPaid = parseFloat(amountPaidInput?.value) || 0;
        const credit = Math.max(0, total - amountPaid);
        const balance = amountPaid - total;

        // Mise à jour des affichages dans l'étape 3
        if (totalDisplay) totalDisplay.textContent = `${total.toFixed(2)} MAD`;
        if (creditDisplay) creditDisplay.textContent = `${credit.toFixed(2)} MAD`;

        // Gestion de l'affichage crédit/rendu seulement dans l'étape 3
        if (!paymentStep3.classList.contains('hidden')) {
            if (balance >= 0) {
                if (creditInfoP) creditInfoP.classList.add('hidden');
                if (changeInfoP) {
                    changeInfoP.classList.remove('hidden');
                    changeInfoP.textContent = `${t('change_due')}: ${balance.toFixed(2)} MAD`;
                }
            } else {
                if (creditInfoP) {
                    creditInfoP.classList.remove('hidden');
                    creditInfoP.textContent = `${t('credit')}: ${(-balance).toFixed(2)} MAD`;
                }
                if (changeInfoP) changeInfoP.classList.add('hidden');
            }
        } else {
            // Masquer les informations si on n'est pas dans l'étape 3
            if (creditInfoP) creditInfoP.classList.add('hidden');
            if (changeInfoP) changeInfoP.classList.add('hidden');
        }
    }

    /**
     * Valide et traite le paiement selon le type
     */
    async function processPayment(paymentType) {
        if (cart.length === 0) {
            showNotification(t('cart_is_empty_alert'), 'warning');
            return;
        }

        try {
            switch (paymentType) {
                case 'cash':
                    await processAndValidateSale(true, 'cash');
                    break;
                case 'check':
                    await processAndValidateSale(true, 'check');
                    break;
                case 'credit':
                    await processAndValidateSale(false, 'credit');
                    break;
                default:
                    console.error('Type de paiement inconnu:', paymentType);
            }
        } catch (error) {
            console.error('Erreur lors du traitement du paiement:', error);
            showNotification(t('payment_processing_error') || 'Erreur lors du traitement du paiement', 'error');
        }
    }

    /**
     * Réinitialise le workflow de paiement
     */
    function resetPaymentWorkflow() {
        // Réinitialiser à l'étape 1 seulement si le panier n'est pas vide
        if (cart.length > 0) {
            showPaymentStep1();
        } else {
            // Si le panier est vide, masquer toutes les étapes
            if (paymentStep1) paymentStep1.classList.add('hidden');
            if (paymentStep2) paymentStep2.classList.add('hidden');
            if (paymentStep3) paymentStep3.classList.add('hidden');
        }

        if (amountPaidInput) amountPaidInput.value = '0';

        // Masquer les informations de crédit/rendu
        if (creditInfoP) creditInfoP.classList.add('hidden');
        if (changeInfoP) changeInfoP.classList.add('hidden');

        updatePartialPaymentDisplay();
        updatePaymentButtonsVisibility();
    }

    async function renderProducts() {
        if (!productGridDiv || isRendering) return;

        isRendering = true;

        try {
            const searchTerm = productSearchInput.value.toLowerCase();
            const productsToDisplay = allProducts.filter(p => {
                const inStock = p.stock > 0;
                const inCategory = selectedCategory === 'all' || p.category === selectedCategory;
                const matchesSearch = p.name.toLowerCase().includes(searchTerm) || (p.barcode && p.barcode.includes(searchTerm));
                return inStock && inCategory && matchesSearch;
            });

            // Utiliser requestAnimationFrame pour éviter le blocage de l'UI
            await new Promise(resolve => requestAnimationFrame(resolve));

            productGridDiv.innerHTML = '';

            if (productsToDisplay.length === 0) {
                productGridDiv.innerHTML = `<p class="text-center text-gray-500 mt-8 col-span-full">${t('no_product_found')}</p>`;
                return;
            }

            const grid = document.createElement('div');
            grid.className = 'grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4';

            // Traitement par lots pour éviter le blocage
            const batchSize = 20;
            for (let i = 0; i < productsToDisplay.length; i += batchSize) {
                const batch = productsToDisplay.slice(i, i + batchSize);

                batch.forEach(p => {
                    const card = document.createElement('div');
                    card.className = 'bg-white dark:bg-gray-800 border dark:border-gray-700 rounded-lg p-3 flex flex-col text-center cursor-pointer hover:shadow-lg hover:border-blue-500 transition-all add-product-btn';
                    card.dataset.productId = p.id;
                    const imageSrc = p.image_path ? p.image_path : 'assets/placeholder.png';
                    card.innerHTML = `
                        <div class="w-full h-20 mb-2 flex items-center justify-center bg-gray-50 dark:bg-gray-700 rounded">
                            <img src="${imageSrc}" alt="${p.name}" class="max-w-full max-h-full object-contain" onerror="this.src='assets/placeholder.png'">
                        </div>
                        <div class="flex-1 flex items-center justify-center min-h-[40px]">
                            <span class="font-bold text-sm text-gray-900 dark:text-white">${p.name}</span>
                        </div>
                        <div class="mt-auto">
                            <span class="text-xs text-gray-500 dark:text-gray-400">Stock: ${p.stock}</span>
                            <p class="font-semibold text-blue-600 dark:text-blue-400">${p.price_retail.toFixed(2)} MAD</p>
                        </div>
                    `;
                    grid.appendChild(card);
                });

                // Pause entre les lots pour permettre à l'UI de respirer
                if (i + batchSize < productsToDisplay.length) {
                    await new Promise(resolve => setTimeout(resolve, 0));
                }
            }

            productGridDiv.appendChild(grid);
        } finally {
            isRendering = false;
        }
    }
    
    function renderCategories() { if (!categoryFiltersDiv) return; categoryFiltersDiv.innerHTML = ''; const allButton = document.createElement('button'); allButton.textContent = t('all_categories'); allButton.className = `px-3 py-1 rounded-full text-sm font-semibold transition ${selectedCategory === 'all' ? 'bg-blue-600 text-white' : 'bg-gray-200 dark:bg-gray-700'}`; allButton.dataset.category = 'all'; categoryFiltersDiv.appendChild(allButton); categories.forEach(cat => { const catButton = document.createElement('button'); catButton.textContent = cat; catButton.className = `px-3 py-1 rounded-full text-sm font-semibold transition ${selectedCategory === cat ? 'bg-blue-600 text-white' : 'bg-gray-200 dark:bg-gray-700'}`; catButton.dataset.category = cat; categoryFiltersDiv.appendChild(catButton); }); }
    
    async function resetSale() {
        cart = [];
        selectedClientId = 1;
        if(clientSearchInput) clientSearchInput.value = '';
        if(clientSearchResultsDiv) clientSearchResultsDiv.classList.add('hidden');

        // Mettre à jour l'affichage du client avec la nouvelle fonction
        await updateClientDisplay(selectedClientId);

        if(amountPaidInput) amountPaidInput.value = '';
        if(productSearchInput) productSearchInput.value = '';
        selectedCategory = 'all';

        // Réinitialiser le workflow de paiement
        resetPaymentWorkflow();

        if (!editMode) {
            await renderProducts();
        }
        renderCategories();
        renderCart();

        // Pas de focus automatique pour éviter les conflits avec les événements clavier
    }
    
    function startCountdown(duration) { let timer = duration; countdownInterval = setInterval(() => { const minutes = Math.floor(timer / 60); const seconds = timer % 60; if(countdownSpan) countdownSpan.textContent = `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`; if (--timer < 0) { clearInterval(countdownInterval); if(editSaleBtn) { editSaleBtn.disabled = true; editSaleBtn.classList.add('opacity-50', 'cursor-not-allowed'); } if(countdownSpan) countdownSpan.textContent = t('expired'); } }, 1000); }
    
    function showLastSalePanel(saleId) { if (countdownInterval) clearInterval(countdownInterval); if(lastSaleIdSpan) lastSaleIdSpan.textContent = `#${saleId}`; originalSaleId = saleId; if(lastSalePanel) lastSalePanel.classList.remove('hidden'); if(editSaleBtn) { editSaleBtn.disabled = false; editSaleBtn.classList.remove('opacity-50', 'cursor-not-allowed'); } startCountdown(300); }
    
    function hideLastSalePanel() { if (countdownInterval) clearInterval(countdownInterval); if(lastSalePanel) lastSalePanel.classList.add('hidden'); }
    
    async function enterEditMode() {
        if (!originalSaleId) return;
        try {
            const saleDetails = await window.api.sales.getDetails(originalSaleId);
            if (!saleDetails) {
                showNotification(t('edit_details_error'), 'error');
                return;
            }
            editMode = true;
            editWorkflowStep = 'products'; // Commencer par l'étape produits

            // Stocker les données originales pour référence
            originalSaleData = {
                client_id: saleDetails.client_id,
                client_name: saleDetails.client_name,
                total_amount: saleDetails.total_amount,
                amount_paid_cash: saleDetails.amount_paid_cash,
                amount_paid_credit: saleDetails.amount_paid_credit,
                payment_method: saleDetails.amount_paid_credit > 0 ? 'credit' : 'cash' // Déduire la méthode
            };
            cart = await Promise.all(saleDetails.items.map(async item => {
                const productInfo = allProducts.find(p => p.id === item.product_id);
                const originalStock = (productInfo ? productInfo.stock : 0) + item.quantity;

                // Déterminer l'unité basée sur le prix unitaire
                let unit = 'retail'; // par défaut
                if (productInfo) {
                    if (Math.abs(item.unit_price - productInfo.price_wholesale) < 0.01) {
                        unit = 'wholesale';
                    } else if (Math.abs(item.unit_price - productInfo.price_carton) < 0.01) {
                        unit = 'carton';
                    }
                }

                return {
                    id: item.product_id,
                    name: item.product_name,
                    quantity: item.quantity,
                    price: item.unit_price,
                    unit: unit,
                    price_retail: productInfo ? productInfo.price_retail : item.unit_price,
                    price_wholesale: productInfo ? productInfo.price_wholesale : item.unit_price,
                    price_carton: productInfo ? productInfo.price_carton : item.unit_price,
                    pieces_per_carton: productInfo ? productInfo.pieces_per_carton : 0,
                    stock: originalStock
                };
            }));
            selectedClientId = saleDetails.client_id;
            await updateClientDisplay(selectedClientId, saleDetails.client_name);
            if(amountPaidInput) amountPaidInput.value = saleDetails.amount_paid_cash.toFixed(2);

            // Adapter l'interface pour l'étape produits du mode édition
            if(validatePaymentBtn) {
                validatePaymentBtn.textContent = t('continue_to_payment_button') || '→ Continuer vers Paiement';
                validatePaymentBtn.classList.remove('bg-green-600', 'hover:bg-green-700');
                validatePaymentBtn.classList.add('bg-blue-600', 'hover:bg-blue-700');
            }

            // Afficher seulement l'étape 1 pour la modification des produits
            showPaymentStep1();
            hideLastSalePanel();
            renderCart();

            // Message d'aide pour le nouveau workflow
            showNotification(
                'Mode modification : Modifiez d\'abord les produits, puis cliquez sur "Continuer vers Paiement" pour corriger la méthode de paiement.',
                'info',
                5000
            );
        } catch (error) {
            console.error("Erreur lors du passage en mode édition:", error);
            showNotification(t('edit_mode_error'), 'error');
        }
    }
    
    function exitEditMode() {
        editMode = false;
        originalSaleId = null;
        editWorkflowStep = 'products';
        originalSaleData = null;

        // Restaurer l'interface normale
        if(validatePaymentBtn) {
            validatePaymentBtn.textContent = t('validate_payment_button') || '✓ Valider Paiement';
            validatePaymentBtn.classList.add('bg-green-600', 'hover:bg-green-700');
            validatePaymentBtn.classList.remove('bg-blue-600', 'hover:bg-blue-700');
            validatePaymentBtn.classList.remove('bg-orange-500', 'hover:bg-orange-600');
        }

        // Nettoyer les indicateurs de paiement
        document.querySelectorAll('.current-payment-indicator').forEach(el => el.remove());

        resetSale();
    }

    /**
     * Met à jour l'affichage du client sélectionné avec son crédit
     */
    async function updateClientDisplay(clientId, clientName = null) {
        try {
            if (clientId === 1) {
                // Client de passage
                if (selectedClientDisplay) selectedClientDisplay.textContent = t('default_client');
                if (clientCreditBadge) clientCreditBadge.classList.add('hidden');
                if (selectedClientContainer) {
                    selectedClientContainer.className = 'flex-shrink-0 text-center bg-blue-50 dark:bg-blue-900/60 p-2 rounded-lg min-w-[150px] transition-colors duration-200';
                }
                if (selectedClientDisplay) {
                    selectedClientDisplay.className = 'font-bold text-blue-800 dark:text-blue-200 truncate flex-1';
                }
                return;
            }

            // Récupérer les informations du client
            const client = await window.api.clients.getById(clientId);
            if (!client) return;

            // Récupérer le crédit du client
            const creditAmount = await window.api.credits.getClientCredit(clientId);

            // Mettre à jour le nom du client
            const displayName = clientName || client.name;
            if (selectedClientDisplay) selectedClientDisplay.textContent = displayName;

            // Mettre à jour l'affichage selon le crédit
            if (creditAmount > 0) {
                // Client avec crédit (dette) - thème rouge
                if (selectedClientContainer) {
                    selectedClientContainer.className = 'flex-shrink-0 text-center bg-red-50 dark:bg-red-900/60 p-2 rounded-lg min-w-[150px] transition-colors duration-200';
                }
                if (selectedClientDisplay) {
                    selectedClientDisplay.className = 'font-bold text-red-800 dark:text-red-200 truncate flex-1';
                }
                if (clientCreditBadge) {
                    clientCreditBadge.textContent = `🔴 ${creditAmount.toFixed(2)} MAD`;
                    clientCreditBadge.className = 'text-xs font-bold px-2 py-1 rounded-full whitespace-nowrap bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100';
                    clientCreditBadge.classList.remove('hidden');
                }
            } else {
                // Client à jour - thème vert
                if (selectedClientContainer) {
                    selectedClientContainer.className = 'flex-shrink-0 text-center bg-green-50 dark:bg-green-900/60 p-2 rounded-lg min-w-[150px] transition-colors duration-200';
                }
                if (selectedClientDisplay) {
                    selectedClientDisplay.className = 'font-bold text-green-800 dark:text-green-200 truncate flex-1';
                }
                if (clientCreditBadge) {
                    clientCreditBadge.textContent = `✅ ${t('client_up_to_date')}`;
                    clientCreditBadge.className = 'text-xs font-bold px-2 py-1 rounded-full whitespace-nowrap bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100';
                    clientCreditBadge.classList.remove('hidden');
                }
            }
        } catch (error) {
            console.error('Erreur lors de la mise à jour de l\'affichage client:', error);
        }
    }

    /**
     * Passe à l'étape paiement en mode édition
     */
    function proceedToPaymentStep() {
        if (!editMode) return;

        editWorkflowStep = 'payment';

        // Changer l'interface pour l'étape paiement
        if(validatePaymentBtn) {
            validatePaymentBtn.textContent = t('validate_payment_button') || '✓ Valider Paiement';
            validatePaymentBtn.classList.remove('bg-blue-600', 'hover:bg-blue-700');
            validatePaymentBtn.classList.add('bg-green-600', 'hover:bg-green-700');
        }

        // Passer à l'étape 2 du workflow de paiement
        showPaymentStep2();

        // Pré-remplir les données de paiement si disponibles
        if (originalSaleData && amountPaidInput) {
            amountPaidInput.value = originalSaleData.amount_paid_cash.toFixed(2);
        }

        // Ajouter une indication visuelle de la méthode actuelle
        addPaymentMethodIndicator();

        showNotification(t('edit_payment_step_info') || 'Choisissez la méthode de paiement pour cette vente', 'info', 3000);
    }

    /**
     * Retourne à l'étape produits en mode édition
     */
    function backToProductsStep() {
        if (!editMode) return;

        editWorkflowStep = 'products';

        // Restaurer l'interface pour l'étape produits
        if(validatePaymentBtn) {
            validatePaymentBtn.textContent = t('continue_to_payment_button') || '→ Continuer vers Paiement';
            validatePaymentBtn.classList.remove('bg-green-600', 'hover:bg-green-700');
            validatePaymentBtn.classList.add('bg-blue-600', 'hover:bg-blue-700');
        }

        // Retourner à l'étape 1
        showPaymentStep1();
    }

    /**
     * Ajoute une indication visuelle de la méthode de paiement actuelle
     */
    function addPaymentMethodIndicator() {
        if (!editMode || !originalSaleData) return;

        // Supprimer les anciens indicateurs
        document.querySelectorAll('.current-payment-indicator').forEach(el => el.remove());

        // Déterminer la méthode actuelle
        let currentMethod = 'cash';
        if (originalSaleData.amount_paid_credit > 0) {
            currentMethod = 'credit';
        }

        // Ajouter l'indicateur sur le bon bouton
        let targetButton = null;
        if (currentMethod === 'cash' && cashPaymentBtn) {
            targetButton = cashPaymentBtn;
        } else if (currentMethod === 'credit' && creditPaymentBtn) {
            targetButton = creditPaymentBtn;
        }

        if (targetButton) {
            const indicator = document.createElement('span');
            indicator.className = 'current-payment-indicator absolute -top-2 -right-2 bg-yellow-500 text-white text-xs px-2 py-1 rounded-full';
            indicator.textContent = 'Actuel';
            indicator.style.position = 'absolute';
            indicator.style.fontSize = '10px';
            indicator.style.zIndex = '10';

            // Positionner le bouton en relatif pour l'indicateur
            targetButton.style.position = 'relative';
            targetButton.appendChild(indicator);
        }
    }

    async function processAndValidateSale(isCashOnly = false, paymentType = 'cash') {
        if (cart.length === 0) {
            showNotification(t('cart_is_empty_alert'), 'warning');
            return;
        }

        if (isProcessing) {
            console.log('Vente déjà en cours de traitement...');
            return;
        }

        isProcessing = true;
        setInputsState(true);

        try {
            const total = cart.reduce((sum, item) => sum + item.quantity * item.price, 0);
            let amountPaidCash = 0;
            let credit = 0;

            // Gestion selon le type de paiement
            switch (paymentType) {
                case 'cash':
                    amountPaidCash = total;
                    credit = 0;
                    break;
                case 'check':
                    amountPaidCash = total;
                    credit = 0;
                    break;
                case 'credit':
                    amountPaidCash = parseFloat(amountPaidInput.value) || 0;
                    credit = Math.max(0, total - amountPaidCash);
                    break;
                default:
                    // Fallback vers l'ancienne logique
                    if (isCashOnly) {
                        amountPaidCash = total;
                        credit = 0;
                    } else {
                        amountPaidCash = parseFloat(amountPaidInput.value) || 0;
                        credit = Math.max(0, total - amountPaidCash);
                    }
            }

            // Validation pour les crédits
            if (credit > 0 && selectedClientId === 1) {
                showNotification(t('credit_for_default_client_error'), 'error');
                return;
            }

            // Validation pour les montants négatifs
            if (amountPaidCash < 0) {
                showNotification('Le montant payé ne peut pas être négatif', 'error');
                return;
            }

            // Validation pour les paiements partiels supérieurs au total
            if (paymentType === 'credit' && amountPaidCash > total) {
                showNotification('Le montant payé ne peut pas être supérieur au total', 'error');
                return;
            }

            const saleCart = cart.map(item => ({
                id: item.id,
                quantity: item.quantity,
                price: item.price,
                unit: item.unit,
                purchase_price: allProducts.find(p => p.id === item.id)?.purchase_price || 0
            }));

            const saleData = {
                clientId: selectedClientId,
                cart: saleCart,
                total,
                amountPaidCash,
                credit,
                paymentMethod: paymentType
            };

            let result;
            if (editMode) {
                // Vérifier qu'on est bien dans l'étape paiement
                if (editWorkflowStep !== 'payment') {
                    showNotification('Erreur: Vous devez d\'abord passer à l\'étape paiement', 'error');
                    return;
                }

                // Mode modification : mettre à jour la vente existante
                result = await window.api.sales.edit({
                    originalSaleId: originalSaleId,
                    newSaleData: saleData
                });
            } else {
                // Mode normal : créer une nouvelle vente
                result = await window.api.sales.process(saleData);
            }

            if (result && result.success) {
                // Préparer les données pour l'impression (asynchrone)
                console.log('Données de vente originales pour impression:', saleData);
                const printData = await prepareSaleDataForPrint(saleData);
                console.log('Données préparées pour impression:', printData);

                if (editMode) {
                    showNotification(t('sale_corrected_success').replace('%s', result.saleId), 'success');
                    // Réinitialiser les variables du mode édition
                    editMode = false;
                    editWorkflowStep = 'products';
                    originalSaleData = null;
                    originalSaleId = null;
                } else {
                    showNotification(t('sale_processed_success').replace('%s', result.saleId), 'success');
                }

                // Réinitialiser d'abord (mais sans masquer le bouton d'impression)
                resetSale();
                resetPaymentWorkflow();

                // Puis afficher le panneau de dernière vente
                showLastSalePanel(result.saleId);

                // Enfin afficher le bouton d'impression
                showPrintSection(printData);

                // Mettre à jour l'affichage du client après la vente (le crédit peut avoir changé)
                await updateClientDisplay(selectedClientId);

                // Recharger les produits de manière asynchrone
                setTimeout(async () => {
                    allProducts = await window.api.products.getAll();
                    await renderProducts();
                    if (window.updateStockAlertBadge) window.updateStockAlertBadge();
                }, 100);
            } else {
                throw new Error(result.error || t('sale_failed_unknown'));
            }
        } catch (error) {
            console.error(t('validation_error'), error);
            showNotification(`Erreur: ${error.message}`, 'error');
        } finally {
            isProcessing = false;
            // Réactiver les champs avec un délai
            setTimeout(() => {
                setInputsState(false);
            }, 200);
        }
    }

    // --- Écouteurs d'événements (logique originale avec vérifications de sécurité) ---
    if (cartItemsTbody) {
        cartItemsTbody.addEventListener('change', e => {
            if (e.target.classList.contains('quantity-input') || e.target.classList.contains('price-input')) {
                const row = e.target.closest('tr'); if (!row) return;
                const productId = parseInt(row.dataset.productId);
                const activeBtn = row.querySelector('.set-price-btn.bg-blue-600'); if (!activeBtn) return;
                const itemIndex = cart.findIndex(i => i.id === productId && i.unit === activeBtn.dataset.priceType);
                if (itemIndex === -1) return;
                const item = cart[itemIndex];
                const newQuantity = parseInt(row.querySelector('.quantity-input').value);
                if (isNaN(newQuantity) || newQuantity < 1) { item.quantity = 1; } 
                else if (newQuantity > item.stock) { showNotification(t('stock_max_reached'), 'warning'); item.quantity = item.stock; }
                else { item.quantity = newQuantity; }
                const newPrice = parseFloat(row.querySelector('.price-input').value);
                if (!isNaN(newPrice) && newPrice >= 0) { item.price = newPrice; }
                renderCart();
            }
        });

        cartItemsTbody.addEventListener('click', e => {
            const row = e.target.closest('tr'); if (!row) return;
            const productId = parseInt(row.dataset.productId);
            const activeBtn = row.querySelector('.set-price-btn.bg-blue-600');
            if (!activeBtn) { if (e.target.classList.contains('remove-item-btn')) { const itemIndex = cart.findIndex(i => i.id === productId); if (itemIndex > -1) cart.splice(itemIndex, 1); renderCart(); } return; }
            const currentUnit = activeBtn.dataset.priceType;
            const itemIndex = cart.findIndex(i => i.id === productId && i.unit === currentUnit);
            if (e.target.classList.contains('remove-item-btn')) {
                if (itemIndex > -1) cart.splice(itemIndex, 1);
                renderCart();
            } else if (e.target.classList.contains('set-price-btn')) {
                if (itemIndex > -1) {
                    const item = cart[itemIndex]; item.unit = e.target.dataset.priceType;
                    if (item.unit === 'retail') item.price = item.price_retail;
                    else if (item.unit === 'wholesale') item.price = item.price_wholesale;
                    else if (item.unit === 'carton') item.price = item.price_carton;
                }
                renderCart();
            }
        });
    }
    
    if (quickAddClientBtn) quickAddClientBtn.addEventListener('click', openAddClientModal);
    if (cancelAddClientBtn) cancelAddClientBtn.addEventListener('click', closeAddClientModal);
    if (addClientForm) addClientForm.addEventListener('submit', async (e) => {
        e.preventDefault();

        const clientData = {
            name: document.getElementById('modal_client_name').value.trim(),
            phone: document.getElementById('modal_client_phone').value.trim(),
            ice: document.getElementById('modal_client_ice').value.trim(),
            address: document.getElementById('modal_client_address').value.trim()
        };

        if (!clientData.name) {
            // Afficher l'erreur dans la modal au lieu d'un alert bloquant
            const nameInput = document.getElementById('modal_client_name');
            if (nameInput) {
                nameInput.style.borderColor = 'red';
                // Pas de focus automatique pour éviter les conflits avec les événements clavier
            }
            showNotification("Le nom du client est obligatoire.", 'error');
            return;
        }

        try {
            const newClient = await window.api.clients.add(clientData);
            if (newClient && newClient.id) {
                // 1. Fermer la modal IMMÉDIATEMENT
                closeAddClientModal();

                // 2. Mettre à jour la sélection du client
                selectedClientId = newClient.id;
                await updateClientDisplay(selectedClientId, newClient.name);

                // 3. Notification de succès non-bloquante
                showNotification(`Client '${newClient.name}' ajouté avec succès !`, 'success');
            }
        } catch (error) {
            console.error('Erreur lors de l\'ajout du client:', error);
            // Notification d'erreur non-bloquante
            showNotification(`L'ajout du client a échoué: ${error.message}`, 'error');
        }
    });
    
    // Ajouter des protections spécifiques pour tous les inputs
    function protectInput(input) {
        if (!input) return;

        input.addEventListener('focus', () => {
            isUserTyping = true;
        });

        input.addEventListener('blur', () => {
            setTimeout(() => {
                isUserTyping = false;
            }, 100);
        });

        input.addEventListener('keydown', (e) => {
            e.stopPropagation(); // Empêcher la propagation vers les gestionnaires globaux
        });

        input.addEventListener('keyup', (e) => {
            e.stopPropagation();
        });

        input.addEventListener('input', (e) => {
            e.stopPropagation();
        });
    }

    // Protéger tous les inputs
    protectInput(productSearchInput);
    protectInput(amountPaidInput);
    protectInput(clientSearchInput);
    protectInput(document.getElementById('modal_client_name'));
    protectInput(document.getElementById('modal_client_phone'));
    protectInput(document.getElementById('modal_client_ice'));
    protectInput(document.getElementById('modal_client_address'));
    protectInput(document.getElementById('barcodeInput'));

    if (productSearchInput) productSearchInput.addEventListener('input', debounce(async () => await renderProducts(), 500));

    // Event listeners pour le scanner code-barres
    const barcodeInput = document.getElementById('barcodeInput');
    if (barcodeInput) {
        // Traitement de l'entrée du code-barres
        barcodeInput.addEventListener('input', async (e) => {
            const barcode = e.target.value.trim();
            if (barcode.length >= 8) { // Code-barres minimum 8 caractères
                await processBarcodeInput(barcode);
            }
        });

        // Traitement de la touche Entrée
        barcodeInput.addEventListener('keydown', async (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                const barcode = e.target.value.trim();
                if (barcode.length > 0) {
                    await processBarcodeInput(barcode);
                }
            }
        });

        // Détection automatique du scanner (basée sur la vitesse de frappe)
        barcodeInput.addEventListener('keydown', (e) => {
            const currentTime = Date.now();
            const timeDiff = currentTime - lastKeyTime;
            lastKeyTime = currentTime;

            // Si l'intervalle entre les touches est très court, c'est probablement un scanner
            if (timeDiff < 50 && timeDiff > 0) {
                isScanning = true;
                // Réinitialiser le timeout
                if (scannerTimeout) clearTimeout(scannerTimeout);
                scannerTimeout = setTimeout(() => {
                    isScanning = false;
                }, 100);
            }
        });

        // Focus automatique sur le champ scanner quand on arrive sur la page
        setTimeout(() => {
            if (barcodeInput && document.activeElement !== barcodeInput) {
                barcodeInput.focus();
            }
        }, 500);
    }

    // Event listener global pour capturer les scans même quand le champ n'est pas focalisé
    document.addEventListener('keydown', (e) => {
        // Si on tape rapidement et que ce n'est pas dans un input, rediriger vers le scanner
        if (!e.target.matches('input, textarea, select') && e.key.match(/[0-9a-zA-Z]/)) {
            const currentTime = Date.now();
            const timeDiff = currentTime - lastKeyTime;

            if (timeDiff < 50 && barcodeInput) {
                // Rediriger le focus vers le scanner
                barcodeInput.focus();
                // Ajouter le caractère au buffer
                barcodeBuffer += e.key;

                // Traiter le buffer après un délai
                if (barcodeTimer) clearTimeout(barcodeTimer);
                barcodeTimer = setTimeout(async () => {
                    if (barcodeBuffer.length >= 8) {
                        barcodeInput.value = barcodeBuffer;
                        await processBarcodeInput(barcodeBuffer);
                    }
                    barcodeBuffer = '';
                }, 100);
            }
            lastKeyTime = currentTime;
        }
    });
    if (categoryFiltersDiv) categoryFiltersDiv.addEventListener('click', async e => { if (e.target.tagName === 'BUTTON') { selectedCategory = e.target.dataset.category; renderCategories(); await renderProducts(); } });
    if (productGridDiv) productGridDiv.addEventListener('click', e => { const card = e.target.closest('.add-product-btn'); if (card) { addProductToCart(parseInt(card.dataset.productId)); } });
    if (amountPaidInput) amountPaidInput.addEventListener('input', updatePartialPaymentDisplay);
    if (clientSearchInput) clientSearchInput.addEventListener('input', debounce(async () => { const searchTerm = clientSearchInput.value; if (searchTerm.length < 2) { if(clientSearchResultsDiv) clientSearchResultsDiv.classList.add('hidden'); return; } try { const clients = await window.api.clients.getAll(searchTerm); if(clientSearchResultsDiv) clientSearchResultsDiv.innerHTML = ''; if (clients.length > 0) { clients.forEach(c => { const itemDiv = document.createElement('div'); itemDiv.className = 'search-result-item p-2 hover:bg-gray-200 dark:hover:bg-gray-600 cursor-pointer'; itemDiv.textContent = `${c.name} (${c.phone || 'N/A'})`; itemDiv.dataset.clientId = c.id; itemDiv.dataset.clientName = c.name; clientSearchResultsDiv.appendChild(itemDiv); }); clientSearchResultsDiv.classList.remove('hidden'); } else { clientSearchResultsDiv.classList.add('hidden'); } } catch (error) { console.error("Erreur pendant la recherche de client:", error); } }, 300));
    if (clientSearchResultsDiv) clientSearchResultsDiv.addEventListener('click', async e => {
        if (e.target.classList.contains('search-result-item')) {
            selectedClientId = parseInt(e.target.dataset.clientId);
            await updateClientDisplay(selectedClientId, e.target.dataset.clientName);
            clientSearchInput.value = '';
            clientSearchResultsDiv.classList.add('hidden');
            /* Pas de focus automatique pour éviter les conflits */
        }
    });
    if (setTotalBtn) setTotalBtn.addEventListener('click', () => { const total = parseFloat(totalAmountSpan.textContent) || 0; amountPaidInput.value = total.toFixed(2); updatePartialPaymentDisplay(); });
    if (cancelSaleBtn) cancelSaleBtn.addEventListener('click', () => { if (editMode) { exitEditMode(); } else { resetSale(); } hideLastSalePanel(); });

    // === NOUVEAUX EVENT LISTENERS POUR LE WORKFLOW DE PAIEMENT ===

    // Étape 1: Valider paiement
    if (validatePaymentBtn) validatePaymentBtn.addEventListener('click', () => {
        if (cart.length === 0) {
            showNotification(t('cart_is_empty_alert'), 'warning');
            return;
        }

        // En mode édition, gérer selon l'étape du workflow
        if (editMode) {
            if (editWorkflowStep === 'products') {
                // Étape produits : passer à l'étape paiement
                proceedToPaymentStep();
                return;
            } else if (editWorkflowStep === 'payment') {
                // Étape paiement : traiter la correction (sera géré par les boutons de paiement)
                showPaymentStep2();
                return;
            }
        }

        // En mode normal, passer à l'étape 2
        showPaymentStep2();
    });

    // Étape 2: Types de paiement
    if (cashPaymentBtn) cashPaymentBtn.addEventListener('click', () => processPayment('cash'));
    if (checkPaymentBtn) checkPaymentBtn.addEventListener('click', () => processPayment('check'));
    if (creditPaymentBtn) creditPaymentBtn.addEventListener('click', () => {
        if (selectedClientId === 1) {
            showNotification(t('credit_for_default_client_error'), 'error');
            return;
        }
        showPaymentStep3();
    });

    // Étape 3: Confirmation paiement partiel
    if (confirmPartialBtn) confirmPartialBtn.addEventListener('click', () => processPayment('credit'));

    // Boutons de retour
    if (backToStep1Btn) backToStep1Btn.addEventListener('click', () => {
        if (editMode && editWorkflowStep === 'payment') {
            // En mode édition, retourner à l'étape produits
            backToProductsStep();
        } else {
            // Mode normal
            showPaymentStep1();
        }
    });

    if (backToStep2Btn) backToStep2Btn.addEventListener('click', showPaymentStep2);
    if (editSaleBtn) editSaleBtn.addEventListener('click', enterEditMode);
    document.addEventListener('click', (e) => { const clientSearchContainer = document.getElementById('clientSearchContainer'); if (clientSearchContainer && !clientSearchContainer.contains(e.target)) { if (clientSearchResultsDiv) clientSearchResultsDiv.classList.add('hidden'); } });

    // Event listeners pour l'impression
    const printTicketBtn = document.getElementById('print-ticket-btn');
    if (printTicketBtn) {
        printTicketBtn.addEventListener('click', openPrintModal);
    }



    const closePrintModalBtn = document.getElementById('closePrintModal');
    if (closePrintModalBtn) {
        closePrintModalBtn.addEventListener('click', closePrintModal);
    }

    const cancelPrintBtn = document.getElementById('cancelPrintBtn');
    if (cancelPrintBtn) {
        cancelPrintBtn.addEventListener('click', closePrintModal);
    }

    const exportPdfBtn = document.getElementById('exportPdfBtn');
    if (exportPdfBtn) {
        exportPdfBtn.addEventListener('click', async () => {
            try {
                closePrintModal();
                if (window.ticketPrinter) {
                    await window.ticketPrinter.exportToPDF();
                } else {
                    console.error('TicketPrinter non disponible pour PDF');
                    showNotification('Erreur: Système d\'impression non disponible', 'error');
                }
            } catch (error) {
                console.error('Erreur lors de l\'export PDF:', error);
                showNotification('Erreur lors de l\'export PDF', 'error');
            }
        });
    }

    const directPrintBtn = document.getElementById('directPrintBtn');
    if (directPrintBtn) {
        directPrintBtn.addEventListener('click', async () => {
            try {
                closePrintModal();
                if (window.ticketPrinter) {
                    await window.ticketPrinter.printDirect();
                } else {
                    console.error('TicketPrinter non disponible pour impression');
                    showNotification('Erreur: Système d\'impression non disponible', 'error');
                }
            } catch (error) {
                console.error('Erreur lors de l\'impression directe:', error);
                showNotification('Erreur lors de l\'impression', 'error');
            }
        });
    }

    // Fermer le modal en cliquant à l'extérieur
    const printModal = document.getElementById('printModal');
    if (printModal) {
        printModal.addEventListener('click', (e) => {
            if (e.target === printModal) {
                closePrintModal();
            }
        });
    }

    // Lancement de l'initialisation de la page
    initPage();
}