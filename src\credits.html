<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="script-src 'self'">
    <title data-i18n="credits_page_title">Gestion des Crédits - Système de Gestion</title>
    <link href="./css/output.css" rel="stylesheet">
    <style>
        /* Styles pour la recherche */
        .search-results-container { position: absolute; z-index: 50; width: 100%; max-height: 150px; overflow-y: auto; background-color: white; border: 1px solid #d1d5db; border-radius: 0 0 0.5rem 0.5rem; box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1); }
        .dark .search-results-container { background-color: #374151; border-color: #4b5563; }
        .search-result-item { padding: 0.5rem; cursor: pointer; }
        .search-result-item:hover { background-color: #f3f4f6; }
        .dark .search-result-item:hover { background-color: #4b5563; }

        /* Styles pour les badges de risque */
        .risk-badge { display: inline-flex; align-items: center; gap: 0.25rem; padding: 0.25rem 0.5rem; border-radius: 9999px; font-size: 0.75rem; font-weight: 600; }
        .risk-badge.low { background-color: #dcfce7; color: #166534; }
        .risk-badge.medium { background-color: #fef3c7; color: #92400e; }
        .risk-badge.high { background-color: #fee2e2; color: #991b1b; }
        .risk-badge.critical { background-color: #1f2937; color: #f87171; }
        .dark .risk-badge.low { background-color: #14532d; color: #bbf7d0; }
        .dark .risk-badge.medium { background-color: #451a03; color: #fde68a; }
        .dark .risk-badge.high { background-color: #450a0a; color: #fecaca; }
        .dark .risk-badge.critical { background-color: #111827; color: #f87171; }

        /* Styles pour les badges d'ancienneté */
        .age-badge { display: inline-flex; align-items: center; gap: 0.25rem; padding: 0.25rem 0.5rem; border-radius: 0.375rem; font-size: 0.75rem; font-weight: 600; }
        .age-badge.recent { background-color: #dcfce7; color: #166534; }
        .age-badge.medium { background-color: #fef3c7; color: #92400e; }
        .age-badge.old { background-color: #fee2e2; color: #991b1b; }
        .dark .age-badge.recent { background-color: #14532d; color: #bbf7d0; }
        .dark .age-badge.medium { background-color: #451a03; color: #fde68a; }
        .dark .age-badge.old { background-color: #450a0a; color: #fecaca; }

        /* Styles pour les étoiles de fiabilité */
        .rating-stars { display: inline-flex; gap: 0.125rem; }
        .star { color: #fbbf24; }
        .star.empty { color: #d1d5db; }
        .dark .star.empty { color: #6b7280; }

        /* Animation pour les lignes du tableau */
        tbody tr { transition: all 0.2s ease-in-out; }
        tbody tr:hover { background-color: #f8fafc; transform: translateY(-1px); box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1); }
        .dark tbody tr:hover { background-color: #1e293b; }

        /* Animation de chargement */
        .loading-skeleton { background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%); background-size: 200% 100%; animation: loading 1.5s infinite; }
        @keyframes loading { 0% { background-position: 200% 0; } 100% { background-position: -200% 0; } }
        .dark .loading-skeleton { background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%); background-size: 200% 100%; }

        /* Styles pour les filtres */
        .filter-btn.active { background-color: #dbeafe !important; color: #1d4ed8 !important; border-color: #3b82f6 !important; }
        .dark .filter-btn.active { background-color: #1e3a8a !important; color: #93c5fd !important; border-color: #3b82f6 !important; }

        /* Styles pour les modals améliorés */
        .modal-content { max-height: 90vh; overflow-y: auto; }

        /* Styles pour les graphiques */
        .metric-card { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .metric-card.green { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
        .metric-card.red { background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); }
        .metric-card.orange { background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); }

        /* Styles pour les lignes cliquables de l'historique */
        .transaction-row-clickable { cursor: pointer !important; transition: all 0.2s ease-in-out; }
        .transaction-row-clickable:hover { background-color: #f0f9ff !important; transform: translateY(-1px); box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1); }
        .dark .transaction-row-clickable:hover { background-color: #1e3a8a !important; }
        .transaction-row-clickable:hover .text-blue-800 { color: #1e40af !important; }
        .transaction-row-clickable:active { transform: translateY(0); }
    </style>
</head>
<body class="bg-gray-100 dark:bg-gray-900 flex h-screen">
    <!-- La sidebar sera créée dynamiquement par sidebar-manager.js -->

    <main class="flex-1 p-8 overflow-hidden flex flex-col">
        <div class="flex flex-col lg:flex-row lg:justify-between lg:items-center mb-6 gap-4 flex-shrink-0">
            <div>
                <h1 class="text-3xl font-bold text-gray-800 dark:text-white mb-2" data-i18n="debtor_clients_title">💳 Gestion des Crédits</h1>
                <div id="creditStats" class="flex flex-wrap gap-4 text-sm">
                    <div class="flex items-center gap-2">
                        <span class="w-3 h-3 bg-blue-500 rounded-full"></span>
                        <span class="text-gray-600 dark:text-gray-400">Total clients: <span id="totalClients" class="font-semibold text-gray-800 dark:text-white">0</span></span>
                    </div>
                    <div class="flex items-center gap-2">
                        <span class="w-3 h-3 bg-red-500 rounded-full"></span>
                        <span class="text-gray-600 dark:text-gray-400">Débiteurs: <span id="debtorClients" class="font-semibold text-red-600">0</span></span>
                    </div>
                    <div class="flex items-center gap-2">
                        <span class="w-3 h-3 bg-purple-500 rounded-full"></span>
                        <span class="text-gray-600 dark:text-gray-400">Crédit total: <span id="totalCredit" class="font-semibold text-purple-600">0 MAD</span></span>
                    </div>
                    <div class="flex items-center gap-2">
                        <span class="w-3 h-3 bg-green-500 rounded-full"></span>
                        <span class="text-gray-600 dark:text-gray-400">Encaissé ce mois: <span id="monthlyCollection" class="font-semibold text-green-600">0 MAD</span></span>
                    </div>
                    <div class="flex items-center gap-2">
                        <span class="w-3 h-3 bg-orange-500 rounded-full"></span>
                        <span class="text-gray-600 dark:text-gray-400">En retard: <span id="overdueClients" class="font-semibold text-orange-600">0</span></span>
                    </div>
                    <div class="flex items-center gap-2">
                        <span class="w-3 h-3 bg-indigo-500 rounded-full"></span>
                        <span class="text-gray-600 dark:text-gray-400">Taux recouvrement: <span id="recoveryRate" class="font-semibold text-indigo-600">0%</span></span>
                    </div>
                </div>
            </div>
            <div class="flex flex-col sm:flex-row gap-3">
                <button id="manualCreditBtn" class="bg-gradient-to-r from-yellow-500 to-yellow-600 text-white px-6 py-3 rounded-lg font-semibold hover:from-yellow-600 hover:to-yellow-700 transition-all duration-200 shadow-lg hover:shadow-xl flex items-center gap-2" data-i18n="credit_adjustment_button">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Nouveau Crédit
                </button>
                <button id="quickPaymentBtn" class="bg-gradient-to-r from-green-600 to-green-700 text-white px-6 py-3 rounded-lg font-semibold hover:from-green-700 hover:to-green-800 transition-all duration-200 shadow-lg hover:shadow-xl flex items-center gap-2">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                    Encaisser
                </button>
                <div class="relative">
                    <button id="exportBtn" class="bg-gradient-to-r from-indigo-600 to-indigo-700 text-white px-6 py-3 rounded-lg font-semibold hover:from-indigo-700 hover:to-indigo-800 transition-all duration-200 shadow-lg hover:shadow-xl flex items-center gap-2">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        Exporter
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                    <div id="exportMenu" class="absolute right-0 mt-2 w-72 bg-white dark:bg-gray-800 rounded-lg shadow-xl border border-gray-200 dark:border-gray-700 z-50 hidden">
                        <div class="py-2">
                            <div class="px-4 py-2 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider border-b border-gray-200 dark:border-gray-600">Format CSV (Excel)</div>
                            <button id="exportAllBtn" class="w-full text-left px-4 py-3 hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center gap-3">
                                <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                <div>
                                    <div class="font-medium text-gray-800 dark:text-white">Rapport Complet CSV</div>
                                    <div class="text-xs text-gray-500">Tous les clients + statistiques</div>
                                </div>
                            </button>
                            <button id="exportDebtorsBtn" class="w-full text-left px-4 py-3 hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center gap-3">
                                <svg class="w-5 h-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <div>
                                    <div class="font-medium text-gray-800 dark:text-white">Débiteurs CSV</div>
                                    <div class="text-xs text-gray-500">Focus sur les créances</div>
                                </div>
                            </button>
                            <button id="exportSummaryBtn" class="w-full text-left px-4 py-3 hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center gap-3">
                                <svg class="w-5 h-5 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                </svg>
                                <div>
                                    <div class="font-medium text-gray-800 dark:text-white">Résumé CSV</div>
                                    <div class="text-xs text-gray-500">Statistiques et métriques</div>
                                </div>
                            </button>

                            <div class="px-4 py-2 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider border-t border-b border-gray-200 dark:border-gray-600 mt-2">Format Excel Natif</div>
                            <button id="exportAllExcelBtn" class="w-full text-left px-4 py-3 hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center gap-3">
                                <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                <div>
                                    <div class="font-medium text-gray-800 dark:text-white">Rapport Excel</div>
                                    <div class="text-xs text-gray-500">Format .xls avec couleurs</div>
                                </div>
                            </button>
                            <button id="exportDebtorsExcelBtn" class="w-full text-left px-4 py-3 hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center gap-3">
                                <svg class="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                <div>
                                    <div class="font-medium text-gray-800 dark:text-white">Débiteurs Excel</div>
                                    <div class="text-xs text-gray-500">Format .xls avec couleurs</div>
                                </div>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Barre de recherche et filtres avancés -->
        <div class="mb-6 bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4 flex-shrink-0">
            <div class="flex flex-col lg:flex-row gap-4">
                <!-- Barre de recherche améliorée -->
                <div class="flex-1 relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </div>
                    <input type="text" id="searchInput" class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white" placeholder="Rechercher un client..." data-i18n-placeholder="search_client_placeholder">
                </div>

                <!-- Filtres de statut -->
                <div class="flex flex-wrap gap-2">
                    <button id="filterAll" class="filter-btn active px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 bg-blue-100 text-blue-700 border border-blue-200 hover:bg-blue-200">
                        Tous
                    </button>
                    <button id="filterDebtors" class="filter-btn px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 bg-gray-100 text-gray-700 border border-gray-200 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-600">
                        Débiteurs
                    </button>
                    <button id="filterCurrent" class="filter-btn px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 bg-gray-100 text-gray-700 border border-gray-200 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-600">
                        À jour
                    </button>
                    <button id="filterOverdue" class="filter-btn px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 bg-gray-100 text-gray-700 border border-gray-200 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-600">
                        En retard
                    </button>
                </div>
            </div>

            <!-- Ligne des outils d'encaissement -->
            <div class="flex flex-col lg:flex-row gap-4 mt-4 pt-4 border-t border-gray-200 dark:border-gray-600">
                <!-- Encaissement rapide -->
                <div class="flex-1 flex gap-2">
                    <div class="relative flex-1">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                        </div>
                        <input type="text" id="quickClientSearch" class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white" placeholder="Client pour encaissement...">
                        <div id="quickClientResults" class="search-results-container hidden"></div>
                    </div>
                    <input type="number" id="quickAmount" class="w-32 px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white" placeholder="Montant" step="0.01" min="0">
                    <button id="quickPayBtn" class="px-4 py-3 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors font-medium">
                        Encaisser
                    </button>
                </div>

                <!-- Filtres avancés -->
                <div class="flex flex-wrap gap-2">
                    <select id="amountFilter" class="px-3 py-2 text-sm border border-gray-300 rounded-lg dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                        <option value="all">Tous montants</option>
                        <option value="small">< 5,000 MAD</option>
                        <option value="medium">5,000 - 10,000 MAD</option>
                        <option value="large">> 10,000 MAD</option>
                    </select>
                    <select id="ageFilter" class="px-3 py-2 text-sm border border-gray-300 rounded-lg dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                        <option value="all">Toutes périodes</option>
                        <option value="recent">< 15 jours</option>
                        <option value="medium">15-30 jours</option>
                        <option value="old">> 30 jours</option>
                    </select>
                </div>
            </div>
        </div>

        <div class="flex-1 bg-white dark:bg-gray-800 shadow-lg rounded-lg overflow-hidden border border-gray-200 dark:border-gray-700 flex flex-col">
            <div class="flex-1 overflow-y-auto" style="min-height: 600px; max-height: calc(100vh - 300px);">
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead class="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-700 dark:to-gray-800 sticky top-0 z-10">
                        <tr>
                            <th class="px-6 py-4 text-left text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider" data-i18n="client_name_header">
                                <div class="flex items-center gap-2">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                    </svg>
                                    Client
                                </div>
                            </th>
                            <th class="px-6 py-4 text-left text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider" data-i18n="phone_header">
                                <div class="flex items-center gap-2">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                    </svg>
                                    Contact
                                </div>
                            </th>
                            <th class="px-6 py-4 text-left text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider" data-i18n="due_amount_header">
                                <div class="flex items-center gap-2">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                    </svg>
                                    Solde Crédit
                                </div>
                            </th>
                            <th class="px-6 py-4 text-center text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider">
                                <div class="flex items-center justify-center gap-2">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                    </svg>
                                    Dernière Transaction
                                </div>
                            </th>
                            <th class="px-6 py-4 text-center text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider">
                                <div class="flex items-center justify-center gap-2">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    Ancienneté
                                </div>
                            </th>
                            <th class="px-6 py-4 text-right text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider" data-i18n="actions_header">
                                <div class="flex items-center justify-end gap-2">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"></path>
                                    </svg>
                                    Actions
                                </div>
                            </th>
                        </tr>
                    </thead>
                    <tbody id="debtorsTableBody" class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    </tbody>
                </table>
            </div>
        </div>
    </main>

    <!-- Modal de paiement amélioré -->
    <div id="paymentModal" class="fixed inset-0 bg-gray-800 bg-opacity-60 hidden items-center justify-center z-40">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl p-6 w-full max-w-lg z-50 modal-content">
            <div class="flex items-center justify-between mb-6">
                <h2 id="modalTitle" class="text-2xl font-bold text-gray-800 dark:text-white" data-i18n="record_payment_modal_title">💰 Enregistrer un Paiement</h2>
                <button id="closeModalBtn" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <!-- Informations client -->
            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-6">
                <div class="flex items-center gap-4 mb-3">
                    <div class="w-12 h-12 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full flex items-center justify-center text-white font-bold text-lg">
                        <span id="clientInitial">A</span>
                    </div>
                    <div>
                        <h3 class="font-semibold text-gray-800 dark:text-white" id="clientName">Client</h3>
                        <p class="text-sm text-gray-600 dark:text-gray-400" id="clientPhone">Téléphone</p>
                    </div>
                </div>
                <div class="grid grid-cols-2 gap-4 text-sm">
                    <div>
                        <span class="text-gray-600 dark:text-gray-400">Dette actuelle:</span>
                        <div class="font-bold text-red-600 text-lg" id="clientDebt">0 MAD</div>
                    </div>
                    <div>
                        <span class="text-gray-600 dark:text-gray-400">Fiabilité:</span>
                        <div class="rating-stars" id="clientRating">
                            <span class="star">★</span><span class="star">★</span><span class="star">★</span><span class="star empty">★</span><span class="star empty">★</span>
                        </div>
                    </div>
                </div>
            </div>

            <form id="paymentForm">
                <input type="hidden" id="clientId">
                <div class="space-y-4">
                    <div>
                        <label for="amount" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2" data-i18n="payment_amount_label">Montant du paiement</label>
                        <div class="relative">
                            <input type="number" step="0.01" id="amount" name="amount" class="w-full pl-4 pr-16 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white text-lg font-semibold" required>
                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                <span class="text-gray-500 text-sm font-medium">MAD</span>
                            </div>
                        </div>
                        <div class="flex gap-2 mt-2">
                            <button type="button" class="quick-amount-btn px-3 py-1 bg-gray-100 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded text-sm hover:bg-gray-200 dark:hover:bg-gray-500" data-amount="100">100</button>
                            <button type="button" class="quick-amount-btn px-3 py-1 bg-gray-100 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded text-sm hover:bg-gray-200 dark:hover:bg-gray-500" data-amount="500">500</button>
                            <button type="button" class="quick-amount-btn px-3 py-1 bg-gray-100 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded text-sm hover:bg-gray-200 dark:hover:bg-gray-500" data-amount="1000">1000</button>
                            <button type="button" id="fullAmountBtn" class="px-3 py-1 bg-green-100 dark:bg-green-800 text-green-700 dark:text-green-300 rounded text-sm hover:bg-green-200 dark:hover:bg-green-700">Tout</button>
                        </div>
                    </div>

                    <div>
                        <label for="paymentNote" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Note (optionnel)</label>
                        <input type="text" id="paymentNote" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white" placeholder="Motif du paiement...">
                    </div>
                </div>

                <div class="mt-6 flex justify-end gap-4">
                    <button type="button" id="closePaymentModalBtn" class="bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 px-6 py-2 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors" data-i18n="cancel_button">Annuler</button>
                    <button type="submit" class="bg-gradient-to-r from-green-600 to-green-700 text-white px-6 py-2 rounded-lg hover:from-green-700 hover:to-green-800 transition-all duration-200 shadow-lg hover:shadow-xl" data-i18n="validate_payment_button">Valider le Paiement</button>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Modal de crédit manuel amélioré -->
    <div id="manualCreditModal" class="fixed inset-0 bg-gray-800 bg-opacity-60 hidden items-center justify-center z-40">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl p-6 w-full max-w-lg z-50 modal-content">
            <div class="flex items-center justify-between mb-6">
                <h2 class="text-2xl font-bold text-gray-800 dark:text-white" data-i18n="manual_credit_modal_title">💳 Ajustement de Crédit</h2>
                <button id="closeManualCreditModalBtn" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <form id="manualCreditForm">
                <div class="space-y-6">
                    <!-- Recherche de client -->
                    <div class="relative">
                        <label for="clientSearchManual" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2" data-i18n="search_client_label">Rechercher un Client</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                            </div>
                            <input type="text" id="clientSearchManual" class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white" autocomplete="off" placeholder="Tapez le nom du client..." data-i18n-placeholder="search_client_placeholder">
                        </div>
                        <div id="clientSearchResults" class="search-results-container hidden"></div>
                    </div>

                    <!-- Client sélectionné -->
                    <div id="selectedClientInfo" class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 hidden">
                        <div class="flex items-center gap-3">
                            <div class="w-10 h-10 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full flex items-center justify-center text-white font-bold">
                                <span id="selectedClientInitial">?</span>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-800 dark:text-white" id="selectedClientName">Aucun client sélectionné</h4>
                                <p class="text-sm text-gray-600 dark:text-gray-400" id="selectedClientDetails">Sélectionnez un client</p>
                            </div>
                        </div>
                    </div>
                    <input type="hidden" id="manualCreditClientId">

                    <!-- Type d'opération -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Type d'opération</label>
                        <div class="grid grid-cols-2 gap-3">
                            <label class="flex items-center p-3 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 dark:border-gray-600">
                                <input type="radio" name="creditType" value="add" class="mr-3" checked>
                                <div>
                                    <div class="font-medium text-green-600">Ajouter crédit</div>
                                    <div class="text-sm text-gray-500">Augmenter le solde</div>
                                </div>
                            </label>
                            <label class="flex items-center p-3 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 dark:border-gray-600">
                                <input type="radio" name="creditType" value="subtract" class="mr-3">
                                <div>
                                    <div class="font-medium text-red-600">Retirer crédit</div>
                                    <div class="text-sm text-gray-500">Diminuer le solde</div>
                                </div>
                            </label>
                        </div>
                    </div>

                    <!-- Montant -->
                    <div>
                        <label for="manualAmount" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2" data-i18n="amount_to_add_label">Montant</label>
                        <div class="relative">
                            <input type="number" step="0.01" id="manualAmount" class="w-full pl-4 pr-16 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white text-lg font-semibold" required min="0">
                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                <span class="text-gray-500 text-sm font-medium">MAD</span>
                            </div>
                        </div>
                    </div>

                    <!-- Motif -->
                    <div>
                        <label for="manualNote" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2" data-i18n="reason_label">Motif</label>
                        <input type="text" id="manualNote" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white" placeholder="Ex: Report ancien solde, Correction..." required>
                    </div>
                </div>

                <div class="mt-6 flex justify-end gap-4">
                    <button type="button" id="closeManualCreditModalBtn2" class="bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 px-6 py-2 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors" data-i18n="cancel_button">Annuler</button>
                    <button type="submit" class="bg-gradient-to-r from-blue-600 to-blue-700 text-white px-6 py-2 rounded-lg hover:from-blue-700 hover:to-blue-800 transition-all duration-200 shadow-lg hover:shadow-xl" data-i18n="add_credit_button">Appliquer l'Ajustement</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Modal d'historique des transactions -->
    <div id="historyModal" class="fixed inset-0 bg-gray-800 bg-opacity-60 hidden items-center justify-center z-40">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl p-6 w-full max-w-4xl z-50 modal-content max-h-[90vh] overflow-y-auto">
            <div class="flex items-center justify-between mb-6">
                <h2 class="text-2xl font-bold text-gray-800 dark:text-white">📊 Historique des Transactions</h2>
                <button id="closeHistoryModalBtn" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <!-- Informations client -->
            <div id="historyClientInfo" class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-6">
                <!-- Sera rempli dynamiquement -->
            </div>

            <!-- Tableau des transactions -->
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead class="bg-gray-50 dark:bg-gray-700">
                        <tr>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                <div class="flex items-center gap-2">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                    </svg>
                                    Date & Heure
                                </div>
                            </th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                <div class="flex items-center gap-2">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a2 2 0 012-2z"></path>
                                    </svg>
                                    Type
                                </div>
                            </th>
                            <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                <div class="flex items-center justify-end gap-2">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                    </svg>
                                    Montant
                                </div>
                            </th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                <div class="flex items-center gap-2">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                    Détails & Motif
                                </div>
                            </th>
                            <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                <div class="flex items-center justify-end gap-2">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                    </svg>
                                    Solde Résultant
                                </div>
                            </th>
                        </tr>
                    </thead>
                    <tbody id="historyTableBody" class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                        <!-- Sera rempli dynamiquement -->
                    </tbody>
                </table>
            </div>

            <!-- Note d'information -->
            <div class="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <div class="flex items-start gap-2">
                    <svg class="w-5 h-5 text-blue-500 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <div class="text-sm text-blue-700 dark:text-blue-300">
                        <p class="font-medium">💡 Astuce :</p>
                        <p>Cliquez sur une ligne de vente pour voir les détails des produits vendus et les informations complètes de la transaction.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal d'encaissement rapide -->
    <div id="quickPaymentModal" class="fixed inset-0 bg-gray-800 bg-opacity-60 hidden items-center justify-center z-40">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl p-6 w-full max-w-md z-50 modal-content">
            <div class="flex items-center justify-between mb-6">
                <h2 class="text-2xl font-bold text-gray-800 dark:text-white">⚡ Encaissement Rapide</h2>
                <button id="closeQuickPaymentModalBtn" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <form id="quickPaymentForm">
                <div class="space-y-4">
                    <!-- Sélection client -->
                    <div class="relative">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Client</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                            </div>
                            <input type="text" id="quickPayClientSearch" class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white" placeholder="Rechercher un client débiteur...">
                        </div>
                        <div id="quickPayClientResults" class="search-results-container hidden"></div>
                    </div>

                    <!-- Client sélectionné -->
                    <div id="quickSelectedClientInfo" class="bg-green-50 dark:bg-green-900/20 rounded-lg p-4 hidden">
                        <!-- Sera rempli dynamiquement -->
                    </div>
                    <input type="hidden" id="quickPayClientId">

                    <!-- Montant -->
                    <div>
                        <label for="quickPayAmount" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Montant à encaisser</label>
                        <div class="relative">
                            <input type="number" step="0.01" id="quickPayAmount" class="w-full pl-4 pr-16 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white text-lg font-semibold" required min="0">
                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                <span class="text-gray-500 text-sm font-medium">MAD</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mt-6 flex justify-end gap-4">
                    <button type="button" id="closeQuickPaymentModalBtn2" class="bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 px-6 py-2 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors">Annuler</button>
                    <button type="submit" class="bg-gradient-to-r from-green-600 to-green-700 text-white px-6 py-2 rounded-lg hover:from-green-700 hover:to-green-800 transition-all duration-200 shadow-lg hover:shadow-xl">Encaisser</button>
                </div>
            </form>
        </div>
    </div>

    <div id="confirmationModal"></div>

    <script src="./js/i18n.js"></script>
    <script src="./js/preloader.js"></script>
    <script src="./js/sidebar-manager.js"></script>
    <script src="./js/page-initializer.js"></script>
    <script src="./js/notifications.js"></script>
    <script src="./js/layout.js"></script>
    <script src="./js/credits.js"></script>
</body>
</html>