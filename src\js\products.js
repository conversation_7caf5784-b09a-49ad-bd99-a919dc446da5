// src/js/products.js - MODIFIÉ POUR L'INTERNATIONALISATION

document.addEventListener('DOMContentLoaded', async () => {
    // ================== DÉBUT DES AJOUTS I18N ==================
    // 1. Charger les traductions et les appliquer au HTML
    await window.i18n.loadTranslations();
    window.i18n.applyTranslationsToHTML();
    // On récupère la fonction t() pour l'utiliser plus facilement
    const t = window.i18n.t;
    // =================== FIN DES AJOUTS I18N ===================

    // La fonction showNotification est maintenant disponible globalement via notifications.js

    // Vérification des API nécessaires
    if (!window.api || !window.api.products || !window.api.session || !window.api.dialog) {
        document.body.innerHTML = "<h1>ERREUR: Une API essentielle est manquante.</h1>";
        return;
    }

    // --- Éléments du DOM ---
    const tableBody = document.getElementById('productsTableBody');
    const searchInput = document.getElementById('searchInput');
    const addProductBtn = document.getElementById('addProductBtn');

    // Nouveaux éléments pour les statistiques et filtres
    const totalProducts = document.getElementById('totalProducts');
    const inStockProducts = document.getElementById('inStockProducts');
    const alertProducts = document.getElementById('alertProducts');
    const outOfStockProducts = document.getElementById('outOfStockProducts');
    const filterAll = document.getElementById('filterAll');
    const filterInStock = document.getElementById('filterInStock');
    const filterAlert = document.getElementById('filterAlert');
    const filterOutOfStock = document.getElementById('filterOutOfStock');
    const productModal = document.getElementById('productModal');
    const cancelBtn = document.getElementById('cancelBtn');
    const productForm = document.getElementById('productForm');
    const modalTitle = document.getElementById('modalTitle');
    const chooseImageBtn = document.getElementById('chooseImageBtn');
    const imagePreview = document.getElementById('imagePreview');
    const imagePathInput = document.getElementById('imagePath');
    let tempImageDataBase64 = null;
    const purchasePriceContainer = document.getElementById('purchasePriceContainer');
    const categoryInput = document.getElementById('category');
    const categoryResults = document.getElementById('categoryResults');
    const categoryContainer = document.getElementById('categoryContainer');
    let allCategories = [];

    // Variables pour la gestion des filtres et données
    let allProducts = [];
    let filteredProducts = [];
    let currentFilter = 'all';

    // Variables pour le scanner code-barres
    let productBarcodeBuffer = '';
    let productBarcodeTimer = null;
    let productLastKeyTime = 0;
    let productIsScanning = false;

    // --- Fonctions du Scanner Code-Barres pour Produits ---

    /**
     * Vérifie si un code-barres existe déjà
     */
    function checkBarcodeExists(barcode) {
        if (!barcode || barcode.trim() === '') return false;

        const cleanBarcode = barcode.trim().replace(/[^\w\-]/g, '');
        return allProducts.some(product => {
            if (!product.barcode) return false;
            const productBarcode = product.barcode.trim().replace(/[^\w\-]/g, '');
            return productBarcode === cleanBarcode;
        });
    }

    /**
     * Traite l'entrée du code-barres dans le formulaire produit
     */
    function processProductBarcodeInput(barcode) {
        if (!barcode || barcode.trim() === '') return;

        const barcodeInput = document.getElementById('barcode');
        const status = document.getElementById('productBarcodeStatus');
        const feedback = document.getElementById('productBarcodeFeedback');
        const message = document.getElementById('productBarcodeMessage');

        try {
            // Mettre à jour le statut
            updateProductBarcodeStatus('scanning');

            // Vérifier si le code-barres existe déjà
            if (checkBarcodeExists(barcode)) {
                showProductBarcodeFeedback('warning', t('barcode_already_exists') || 'Ce code-barres existe déjà');
            } else {
                // Code-barres valide et unique
                showProductBarcodeFeedback('success', t('barcode_valid') || 'Code-barres valide');
            }

            // Remettre le statut à prêt
            setTimeout(() => {
                updateProductBarcodeStatus('ready');
            }, 1000);

        } catch (error) {
            console.error('Erreur lors du traitement du code-barres produit:', error);
            showProductBarcodeFeedback('error', t('barcode_scan_error') || 'Erreur lors du scan');
            updateProductBarcodeStatus('ready');
        }
    }

    /**
     * Met à jour le statut du scanner de produit
     */
    function updateProductBarcodeStatus(status) {
        const statusElement = document.getElementById('productBarcodeStatus');
        if (!statusElement) return;

        const statusDot = statusElement.querySelector('.w-2.h-2');
        const statusText = statusElement.querySelector('span');

        if (statusDot && statusText) {
            switch (status) {
                case 'ready':
                    statusDot.className = 'w-2 h-2 bg-green-500 rounded-full animate-pulse';
                    statusText.textContent = t('scanner_ready') || 'Prêt';
                    break;
                case 'scanning':
                    statusDot.className = 'w-2 h-2 bg-blue-500 rounded-full animate-spin';
                    statusText.textContent = t('scanner_scanning') || 'Scan en cours...';
                    break;
                case 'error':
                    statusDot.className = 'w-2 h-2 bg-red-500 rounded-full';
                    statusText.textContent = 'Erreur';
                    break;
                case 'warning':
                    statusDot.className = 'w-2 h-2 bg-yellow-500 rounded-full animate-pulse';
                    statusText.textContent = 'Attention';
                    break;
            }
        }
    }

    /**
     * Affiche un feedback du scanner de produit
     */
    function showProductBarcodeFeedback(type, message) {
        const feedback = document.getElementById('productBarcodeFeedback');
        const messageElement = document.getElementById('productBarcodeMessage');

        if (!feedback || !messageElement) return;

        // Définir les couleurs selon le type
        let bgColor, textColor, iconPath;

        switch (type) {
            case 'success':
                bgColor = 'bg-green-100 dark:bg-green-900/30';
                textColor = 'text-green-800 dark:text-green-200';
                iconPath = 'M5 13l4 4L19 7'; // Checkmark
                break;
            case 'warning':
                bgColor = 'bg-yellow-100 dark:bg-yellow-900/30';
                textColor = 'text-yellow-800 dark:text-yellow-200';
                iconPath = 'M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z'; // Warning
                break;
            case 'error':
                bgColor = 'bg-red-100 dark:bg-red-900/30';
                textColor = 'text-red-800 dark:text-red-200';
                iconPath = 'M6 18L18 6M6 6l12 12'; // X
                break;
            default:
                bgColor = 'bg-blue-100 dark:bg-blue-900/30';
                textColor = 'text-blue-800 dark:text-blue-200';
                iconPath = 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'; // Info
        }

        // Mettre à jour le contenu
        feedback.className = `mt-1 text-sm ${bgColor} ${textColor} rounded-lg`;
        feedback.innerHTML = `
            <div class="flex items-center gap-2 p-2">
                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="${iconPath}"></path>
                </svg>
                <span>${message}</span>
            </div>
        `;

        // Afficher le feedback
        feedback.classList.remove('hidden');

        // Masquer après 3 secondes
        setTimeout(() => {
            if (feedback) {
                feedback.classList.add('hidden');
            }
        }, 3000);
    }

    function showLoadingSkeleton() {
        tableBody.innerHTML = '';
        for (let i = 0; i < 5; i++) {
            const tr = document.createElement('tr');
            tr.innerHTML = `
                <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 h-10 w-10">
                            <div class="h-10 w-10 rounded-lg loading-skeleton"></div>
                        </div>
                        <div class="ml-4 space-y-2">
                            <div class="h-4 w-32 loading-skeleton rounded"></div>
                            <div class="h-3 w-24 loading-skeleton rounded"></div>
                        </div>
                    </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <div class="h-4 w-20 loading-skeleton rounded"></div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <div class="h-4 w-16 loading-skeleton rounded"></div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <div class="h-4 w-16 loading-skeleton rounded"></div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <div class="h-6 w-20 loading-skeleton rounded"></div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-right">
                    <div class="flex items-center justify-end gap-2">
                        <div class="h-8 w-16 loading-skeleton rounded"></div>
                        <div class="h-8 w-16 loading-skeleton rounded"></div>
                    </div>
                </td>
            `;
            tableBody.appendChild(tr);
        }
    }

    async function loadProducts(searchTerm = '') {
        try {
            showLoadingSkeleton();
            allProducts = await window.api.products.getAll(searchTerm);
            applyCurrentFilter();
        } catch (error) {
            console.error(t('error_loading_products'), error);
            showNotification(t('error_loading_products'), 'error');
            tableBody.innerHTML = `<tr><td colspan="6" class="text-center py-8 text-red-500">${t('error_loading_products')}</td></tr>`;
        }
    }

    function applyCurrentFilter() {
        // Filtrer les produits selon le filtre actuel
        switch (currentFilter) {
            case 'inStock':
                filteredProducts = allProducts.filter(p => p.stock > 0 && (p.alert_threshold === 0 || p.stock > p.alert_threshold));
                break;
            case 'alert':
                filteredProducts = allProducts.filter(p => p.alert_threshold > 0 && p.stock <= p.alert_threshold && p.stock > 0);
                break;
            case 'outOfStock':
                filteredProducts = allProducts.filter(p => p.stock <= 0);
                break;
            default:
                filteredProducts = [...allProducts];
        }

        renderProducts();
        updateStatistics();
    }

    function renderProducts() {
        tableBody.innerHTML = '';

        if (filteredProducts.length === 0) {
            tableBody.innerHTML = `<tr><td colspan="6" class="text-center py-8 text-gray-500 dark:text-gray-400">${t('no_product_found')}</td></tr>`;
            return;
        }

        filteredProducts.forEach(p => {
            const tr = document.createElement('tr');
            tr.className = 'hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200';

            // Déterminer le statut du stock
            let stockStatus = 'in-stock';
            let stockBadge = '';
            let stockIcon = '🟢';

            if (p.stock <= 0) {
                stockStatus = 'out-of-stock';
                stockIcon = '🔴';
                stockBadge = `<span class="stock-badge out-of-stock">${stockIcon} Rupture</span>`;
            } else if (p.alert_threshold > 0 && p.stock <= p.alert_threshold) {
                stockStatus = 'alert';
                stockIcon = '🟡';
                stockBadge = `<span class="stock-badge alert">${stockIcon} Alerte</span>`;
            } else {
                stockBadge = `<span class="stock-badge in-stock">${stockIcon} En stock</span>`;
            }

            tr.innerHTML = `
                <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 h-10 w-10">
                            <div class="h-10 w-10 rounded-lg bg-gradient-to-br from-blue-400 to-blue-600 flex items-center justify-center text-white font-bold text-sm">
                                ${p.name.charAt(0).toUpperCase()}
                            </div>
                        </div>
                        <div class="ml-4">
                            <div class="text-sm font-medium text-gray-900 dark:text-white">${p.name}</div>
                            <div class="text-sm text-gray-500 dark:text-gray-400">${p.category || 'Sans catégorie'}</div>
                        </div>
                    </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900 dark:text-white font-mono">${p.barcode || '<span class="text-gray-400">N/A</span>'}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm font-semibold text-gray-900 dark:text-white">${p.price_retail.toFixed(2)} <span class="text-gray-500">MAD</span></div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm font-semibold text-gray-900 dark:text-white">${p.price_wholesale.toFixed(2)} <span class="text-gray-500">MAD</span></div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center gap-2">
                        <span class="text-lg font-bold text-gray-900 dark:text-white">${p.stock}</span>
                        ${stockBadge}
                    </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div class="flex items-center justify-end gap-2">
                        <button class="action-btn edit edit-btn" data-id="${p.id}">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                            </svg>
                            ${t('edit')}
                        </button>
                        <button class="action-btn delete delete-btn" data-id="${p.id}">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                            ${t('delete')}
                        </button>
                    </div>
                </td>
            `;

            tableBody.appendChild(tr);
        });
    }

    function updateStatistics() {
        const total = allProducts.length;
        const inStock = allProducts.filter(p => p.stock > 0 && (p.alert_threshold === 0 || p.stock > p.alert_threshold)).length;
        const alert = allProducts.filter(p => p.alert_threshold > 0 && p.stock <= p.alert_threshold && p.stock > 0).length;
        const outOfStock = allProducts.filter(p => p.stock <= 0).length;

        if (totalProducts) totalProducts.textContent = total;
        if (inStockProducts) inStockProducts.textContent = inStock;
        if (alertProducts) alertProducts.textContent = alert;
        if (outOfStockProducts) outOfStockProducts.textContent = outOfStock;
    }

    function openModal() {
        productModal.classList.replace('hidden', 'flex');
        // Pas de focus automatique pour éviter les conflits avec les événements clavier
    }
    
    function closeModal() {
        productModal.classList.replace('flex', 'hidden');
        productForm.reset();
        document.getElementById('productId').value = '';
        imagePreview.src = 'assets/placeholder.png';
        imagePathInput.value = '';
        tempImageDataBase64 = null;
        // Pas de focus automatique pour éviter les conflits avec les événements clavier
    }
    
    function renderCategorySuggestions(categories) {
        categoryResults.innerHTML = '';
        if (categories.length === 0) {
            categoryResults.classList.add('hidden');
            return;
        }
        categories.forEach(cat => {
            const itemDiv = document.createElement('div');
            itemDiv.textContent = cat;
            itemDiv.className = 'category-result-item';
            itemDiv.addEventListener('click', () => {
                categoryInput.value = cat;
                categoryResults.classList.add('hidden');
            });
            categoryResults.appendChild(itemDiv);
        });
        categoryResults.classList.remove('hidden');
    }

    categoryInput.addEventListener('focus', () => {
        renderCategorySuggestions(allCategories);
    });

    categoryInput.addEventListener('input', () => {
        const searchTerm = categoryInput.value.toLowerCase();
        const filteredCategories = allCategories.filter(cat => cat.toLowerCase().includes(searchTerm));
        renderCategorySuggestions(filteredCategories);
    });
    
    document.addEventListener('click', (e) => {
        if (!categoryContainer.contains(e.target)) {
            categoryResults.classList.add('hidden');
        }
    });

    addProductBtn.addEventListener('click', () => {
        // On traduit le titre de la modale
        modalTitle.textContent = t('add_product_modal_title');
        openModal();
    });

    cancelBtn.addEventListener('click', closeModal);
    // Fonction debounce pour la recherche
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    const debouncedSearch = debounce((searchTerm) => {
        loadProducts(searchTerm);
    }, 300);

    searchInput.addEventListener('input', (e) => {
        debouncedSearch(e.target.value);
    });

    // --- Fonctions pour les filtres ---
    function setActiveFilter(filterType) {
        currentFilter = filterType;

        // Mettre à jour les styles des boutons
        document.querySelectorAll('.filter-btn').forEach(btn => btn.classList.remove('active'));

        switch (filterType) {
            case 'all':
                if (filterAll) filterAll.classList.add('active');
                break;
            case 'inStock':
                if (filterInStock) filterInStock.classList.add('active');
                break;
            case 'alert':
                if (filterAlert) filterAlert.classList.add('active');
                break;
            case 'outOfStock':
                if (filterOutOfStock) filterOutOfStock.classList.add('active');
                break;
        }

        applyCurrentFilter();
    }

    // Event listeners pour les filtres
    if (filterAll) filterAll.addEventListener('click', () => setActiveFilter('all'));
    if (filterInStock) filterInStock.addEventListener('click', () => setActiveFilter('inStock'));
    if (filterAlert) filterAlert.addEventListener('click', () => setActiveFilter('alert'));
    if (filterOutOfStock) filterOutOfStock.addEventListener('click', () => setActiveFilter('outOfStock'));

    chooseImageBtn.addEventListener('click', async () => {
        const dataUrl = await window.api.dialog.openImage();
        if (dataUrl) {
            tempImageDataBase64 = dataUrl;
            imagePreview.src = dataUrl;
        }
    });

    // Event listeners pour le scanner code-barres dans le formulaire produit
    const barcodeInput = document.getElementById('barcode');
    if (barcodeInput) {
        // Traitement de l'entrée du code-barres
        barcodeInput.addEventListener('input', (e) => {
            const barcode = e.target.value.trim();
            if (barcode.length >= 6) { // Code-barres minimum 6 caractères pour les produits
                processProductBarcodeInput(barcode);
            }
        });

        // Traitement de la touche Entrée
        barcodeInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                const barcode = e.target.value.trim();
                if (barcode.length > 0) {
                    processProductBarcodeInput(barcode);
                }
            }
        });

        // Détection automatique du scanner (basée sur la vitesse de frappe)
        barcodeInput.addEventListener('keydown', (e) => {
            const currentTime = Date.now();
            const timeDiff = currentTime - productLastKeyTime;
            productLastKeyTime = currentTime;

            // Si l'intervalle entre les touches est très court, c'est probablement un scanner
            if (timeDiff < 50 && timeDiff > 0) {
                productIsScanning = true;
                updateProductBarcodeStatus('scanning');

                // Réinitialiser le timeout
                if (productBarcodeTimer) clearTimeout(productBarcodeTimer);
                productBarcodeTimer = setTimeout(() => {
                    productIsScanning = false;
                    updateProductBarcodeStatus('ready');
                }, 200);
            }
        });

        // Validation en temps réel lors de la perte de focus
        barcodeInput.addEventListener('blur', (e) => {
            const barcode = e.target.value.trim();
            if (barcode.length > 0) {
                processProductBarcodeInput(barcode);
            }
        });

        // Focus automatique sur le champ code-barres quand le modal s'ouvre
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                    const modal = document.getElementById('productModal');
                    if (modal && !modal.classList.contains('hidden')) {
                        // Modal ouvert, focus sur le code-barres après un délai
                        setTimeout(() => {
                            if (barcodeInput && document.activeElement !== barcodeInput) {
                                barcodeInput.focus();
                            }
                        }, 300);
                    }
                }
            });
        });

        const modal = document.getElementById('productModal');
        if (modal) {
            observer.observe(modal, { attributes: true });
        }
    }

    tableBody.addEventListener('click', async (e) => {
        const editButton = e.target.closest('.edit-btn');
        const deleteButton = e.target.closest('.delete-btn');

        if (editButton) {
            const id = editButton.dataset.id;
            const product = await window.api.products.getById(id);
            if (!product) return;
            
            // On traduit le titre de la modale
            modalTitle.textContent = t('edit_product_modal_title');
            document.getElementById('productId').value = product.id;
            document.getElementById('name').value = product.name;
            document.getElementById('barcode').value = product.barcode;
            document.getElementById('purchase_price').value = product.purchase_price;
            document.getElementById('price_retail').value = product.price_retail;
            document.getElementById('price_wholesale').value = product.price_wholesale;
            document.getElementById('stock').value = product.stock;
            document.getElementById('alert_threshold').value = product.alert_threshold;
            document.getElementById('category').value = product.category;
            document.getElementById('price_carton').value = product.price_carton;
            document.getElementById('pieces_per_carton').value = product.pieces_per_carton;
            imagePathInput.value = product.image_path || '';
            tempImageDataBase64 = null; 
            imagePreview.src = product.image_path || 'assets/placeholder.png';
            
            openModal();
        }

        if (deleteButton) {
            const id = deleteButton.dataset.id;
            // On traduit le message de confirmation
            const confirmed = await showConfirmation(t('confirm_delete_product'));
            if (confirmed) {
                try {
                    await window.api.products.delete(id);
                    loadProducts(searchInput.value); 
                    if (window.updateStockAlertBadge) window.updateStockAlertBadge();
                } catch (error) {
                    showNotification(t('delete_failed') + ': ' + error.message, 'error');
                }
            }
        }
    });

    productForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        const id = document.getElementById('productId').value;
        const productData = {
            name: document.getElementById('name').value,
            barcode: document.getElementById('barcode').value,
            purchase_price: parseFloat(document.getElementById('purchase_price').value) || 0,
            price_retail: parseFloat(document.getElementById('price_retail').value),
            price_wholesale: parseFloat(document.getElementById('price_wholesale').value),
            stock: parseInt(document.getElementById('stock').value),
            alert_threshold: parseInt(document.getElementById('alert_threshold').value),
            category: document.getElementById('category').value,
            image_path: tempImageDataBase64 || imagePathInput.value,
            price_carton: parseFloat(document.getElementById('price_carton').value) || 0,
            pieces_per_carton: parseInt(document.getElementById('pieces_per_carton').value) || 0,
        };

        if (!productData.name || isNaN(productData.price_retail)) {
            showNotification(t('error_name_price_required'), 'error');
            return;
        }

        try {
            if (id) {
                productData.id = parseInt(id);
                await window.api.products.update(productData);
            } else {
                await window.api.products.add(productData);
            }
            
            closeModal();
            loadProducts(searchInput.value);
            allCategories = await window.api.products.getCategories();
            if(window.updateStockAlertBadge) window.updateStockAlertBadge();

        } catch (error) {
            console.error(t('error_saving_product'), error);
            if (error.message.includes('UNIQUE constraint failed: products.barcode')) {
                showNotification(t('error_barcode_unique'), 'error');
            } else {
                showNotification(t('operation_failed') + ': ' + error.message, 'error');
            }
        }
    });

    async function initPage() {
        // La traduction est déjà chargée en haut du script
        await initializePage('products');
        
        const user = await window.api.session.getCurrentUser();
        if (user.role !== 'Propriétaire') {
            if (purchasePriceContainer) {
                purchasePriceContainer.style.display = 'none';
            }
        }
        
        try {
            allCategories = await window.api.products.getCategories();
        } catch (error) {
            console.error("Erreur lors du chargement des catégories:", error);
        }

        loadProducts();
    }

    initPage();
});