{"name": "projet-gestion-maitre", "version": "1.0.0", "description": "Application de bureau Maître pour la gestion complète.", "main": "main.js", "scripts": {"start": "electron .", "watch:css": "tailwindcss -i ./src/css/input.css -o ./src/css/output.css --watch", "dev:electron": "nodemon --exec \"electron .\"", "dev": "concurrently \"npm:watch:css\" \"npm:dev:electron\"", "dist": "electron-builder"}, "build": {"appId": "com.votre-societe.gestionpro", "productName": "GestionPro", "win": {"target": "nsis", "icon": "src/assets/icons/icon.ico"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true}}, "nodemonConfig": {"ignore": ["database/*", "src/css/output.css"], "delay": 1500}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"bcrypt": "^5.1.1", "better-sqlite3": "^9.4.3", "node-machine-id": "^1.1.12"}, "devDependencies": {"concurrently": "^8.2.2", "electron": "^28.3.3", "electron-builder": "^24.9.1", "electron-rebuild": "^3.2.9", "nodemon": "^3.1.4", "tailwindcss": "^3.4.4"}}