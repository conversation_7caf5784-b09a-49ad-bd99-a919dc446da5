// src/js/sidebar-manager.js - Gestionnaire centralisé de la sidebar

/**
 * Gestionnaire centralisé de la sidebar pour éviter les duplications
 */
class SidebarManager {
    constructor() {
        this.isInitialized = false;
        this.sidebarElement = null;
    }

    /**
     * Initialise la sidebar une seule fois
     */
    init() {
        if (this.isInitialized) {
            return;
        }

        // Supprimer toutes les sidebars existantes
        this.removeAllSidebars();

        // Créer la sidebar unique
        this.createSidebar();

        // S'assurer que le body a les bonnes classes
        this.ensureBodyLayout();

        this.isInitialized = true;
        console.log('SidebarManager: Sidebar initialisée');
    }

    /**
     * Supprime toutes les sidebars existantes
     */
    removeAllSidebars() {
        const sidebars = document.querySelectorAll('aside.w-64, aside[class*="bg-gray-800"]');
        sidebars.forEach(sidebar => {
            sidebar.remove();
            console.log('SidebarManager: Sidebar supprimée');
        });
    }

    /**
     * <PERSON><PERSON>e la sidebar unique
     */
    createSidebar() {
        this.sidebarElement = document.createElement('aside');
        this.sidebarElement.className = 'w-64 bg-gray-800 text-white flex flex-col flex-shrink-0';
        this.sidebarElement.innerHTML = `
            <div class="p-4 text-2xl font-bold border-b border-gray-700">GestionPro</div>
            <nav id="main-nav" class="flex-grow"></nav>
        `;

        // Insérer au début du body
        document.body.insertBefore(this.sidebarElement, document.body.firstChild);
        console.log('SidebarManager: Nouvelle sidebar créée');
    }

    /**
     * S'assure que le body a les bonnes classes pour le layout
     */
    ensureBodyLayout() {
        const body = document.body;
        
        // Ajouter les classes nécessaires si elles n'existent pas
        if (!body.classList.contains('flex')) {
            body.classList.add('flex');
        }
        if (!body.classList.contains('h-screen')) {
            body.classList.add('h-screen');
        }

        // S'assurer que le main a les bonnes classes
        const main = document.querySelector('main');
        if (main && !main.classList.contains('flex-1')) {
            main.classList.add('flex-1');
        }
    }

    /**
     * Nettoie et réinitialise la sidebar
     */
    reset() {
        this.isInitialized = false;
        this.sidebarElement = null;
        this.removeAllSidebars();
    }

    /**
     * Vérifie si la sidebar existe et est valide
     */
    isValid() {
        const sidebar = document.querySelector('aside.w-64');
        const nav = document.getElementById('main-nav');
        return sidebar && nav && sidebar.contains(nav);
    }

    /**
     * Force la réinitialisation si nécessaire
     */
    ensureValid() {
        if (!this.isValid()) {
            console.log('SidebarManager: Sidebar invalide, réinitialisation...');
            this.reset();
            this.init();
        }
    }
}

// Instance globale
const sidebarManager = new SidebarManager();

/**
 * Fonction utilitaire pour initialiser la sidebar
 */
function initializeSidebar() {
    sidebarManager.init();
}

/**
 * Fonction utilitaire pour s'assurer que la sidebar est valide
 */
function ensureSidebarValid() {
    sidebarManager.ensureValid();
}

/**
 * Fonction utilitaire pour supprimer les sidebars dupliquées
 */
function removeDuplicateSidebars() {
    sidebarManager.removeAllSidebars();
}

// Exposer globalement
window.sidebarManager = sidebarManager;
window.initializeSidebar = initializeSidebar;
window.ensureSidebarValid = ensureSidebarValid;
window.removeDuplicateSidebars = removeDuplicateSidebars;

// Auto-initialisation au chargement du DOM
document.addEventListener('DOMContentLoaded', () => {
    // Attendre un peu pour que les autres scripts se chargent
    setTimeout(() => {
        initializeSidebar();
    }, 100);
});
