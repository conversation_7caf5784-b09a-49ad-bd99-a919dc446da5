// main.js - MODIFIÉ POUR L'INTERNATIONALISATION
const { app, BrowserWindow, ipcMain, nativeTheme, dialog } = require('electron');
const path = require('path');
const fs = require('fs');
const { machineIdSync } = require('node-machine-id');
const db = require('./database.js');

app.disableHardwareAcceleration();

const activationFilePath = path.join(app.getPath('userData'), 'activation.json');
let currentUser = null;
let mainWindow;

try {
    db.initDatabase();
} catch (err) {
    console.error("Erreur critique lors de l'initialisation de la DB:", err);
    app.quit();
}

function createMainWindow() {
    mainWindow = new BrowserWindow({
        width: 1280,
        height: 800,
        show: false,
        webPreferences: {
            preload: path.join(__dirname, 'preload.js'),
        }
    });
    const savedTheme = db.settingsDB.get('theme');
    if (savedTheme) nativeTheme.themeSource = savedTheme;
    mainWindow.on('closed', () => { mainWindow = null; });
}

function createModalWindow(options, file) {
    const window = new BrowserWindow({
        width: options.width,
        height: options.height,
        frame: false,
        resizable: false,
        parent: mainWindow,
        modal: true,
        show: false,
        webPreferences: {
            preload: path.join(__dirname, 'preload.js')
        }
    });
    window.loadFile(file);
    window.once('ready-to-show', () => window.show());
    return window;
}

app.whenReady().then(() => {
    createMainWindow();
    try {
        if (fs.existsSync(activationFilePath)) {
            const activationData = JSON.parse(fs.readFileSync(activationFilePath));
            if (activationData && activationData.activated) {
                createModalWindow({ width: 400, height: 500 }, 'login.html');
            } else {
                createModalWindow({ width: 500, height: 350 }, 'activation.html');
            }
        } else {
            createModalWindow({ width: 500, height: 350 }, 'activation.html');
        }
    } catch (error) {
        console.error("Erreur au démarrage:", error);
        createModalWindow({ width: 500, height: 350 }, 'activation.html');
    }
});

app.on('window-all-closed', () => { if (process.platform !== 'darwin') app.quit(); });

// Fonctions de gestion des requêtes IPC
async function handleRequest(handler, ipcChannel) {
    console.time(`IPC: ${ipcChannel}`);
    try {
        return await handler();
    } catch (error) {
        console.error(`Erreur IPC sur '${ipcChannel}':`, error.message);
        throw error;
    } finally {
        console.timeEnd(`IPC: ${ipcChannel}`);
    }
}

async function handleUserRequest(handler, ipcChannel) {
    if (!currentUser || !currentUser.id) throw new Error("Aucun utilisateur n'est connecté.");
    return handleRequest(handler, ipcChannel);
}

// --- Canaux IPC ---
ipcMain.handle('app:get-machine-id', () => handleRequest(() => machineIdSync({ original: true }), 'app:get-machine-id'));
ipcMain.handle('session:get-current-user', () => handleRequest(() => currentUser, 'session:get-current-user'));
ipcMain.handle('users:authenticate', async (event, { username, password }) => { const user = await handleRequest(() => db.usersDB.authenticateUser(username, password), 'users:authenticate'); if (user) { currentUser = user; await mainWindow.loadFile(user.role === 'Propriétaire' ? 'src/index.html' : 'src/caisse.html'); mainWindow.show(); BrowserWindow.fromWebContents(event.sender)?.close(); } return user; });
ipcMain.on('app:activation-success', (event) => { BrowserWindow.fromWebContents(event.sender)?.close(); createModalWindow({ width: 400, height: 500 }, 'login.html'); });

// --- Gestion de l'activation de licence ---
ipcMain.handle('activate-license', async (event, licenseKey) => {
    return handleRequest(async () => {
        try {
            // Obtenir l'ID de la machine
            const machineId = machineIdSync({ original: true });

            // Ici vous pouvez ajouter votre logique de validation de licence
            // Pour l'instant, nous allons créer une validation basique
            if (!licenseKey || licenseKey.length < 10) {
                return { success: false, message: 'Clé de licence invalide' };
            }

            // Simuler une validation de licence (remplacez par votre logique réelle)
            // Par exemple, vous pourriez faire une requête HTTP vers votre serveur de licences
            const isValidLicense = await validateLicenseKey(licenseKey, machineId);

            if (isValidLicense) {
                // Sauvegarder l'activation
                const activationData = {
                    activated: true,
                    licenseKey: licenseKey,
                    machineId: machineId,
                    activationDate: new Date().toISOString()
                };

                fs.writeFileSync(activationFilePath, JSON.stringify(activationData, null, 2));

                // Fermer la fenêtre d'activation et ouvrir la fenêtre de connexion
                BrowserWindow.fromWebContents(event.sender)?.close();
                createModalWindow({ width: 400, height: 500 }, 'login.html');

                return { success: true, message: 'Activation réussie' };
            } else {
                return { success: false, message: 'Clé de licence invalide ou déjà utilisée' };
            }
        } catch (error) {
            console.error('Erreur lors de l\'activation:', error);
            return { success: false, message: 'Erreur lors de l\'activation' };
        }
    }, 'activate-license');
});

// Fonction de validation de licence (à personnaliser selon vos besoins)
async function validateLicenseKey(licenseKey, machineId) {
    // Implémentation basique - remplacez par votre logique réelle
    // Vous pourriez faire une requête HTTP vers votre serveur de licences ici

    // Pour l'instant, acceptons toutes les clés qui ont au moins 10 caractères
    // et qui ne sont pas des clés de test évidentes
    const invalidKeys = ['test', '**********', 'invalid'];

    if (invalidKeys.some(invalid => licenseKey.toLowerCase().includes(invalid))) {
        return false;
    }

    // Simuler un délai de validation
    await new Promise(resolve => setTimeout(resolve, 1000));

    return licenseKey.length >= 10;
}
ipcMain.handle('dialog:open-image', async () => { const { canceled, filePaths } = await dialog.showOpenDialog(mainWindow, { properties: ['openFile'], filters: [{ name: 'Images', extensions: ['jpg', 'jpeg', 'png'] }] }); if (canceled || filePaths.length === 0) return null; try { const filePath = filePaths[0]; const fileData = fs.readFileSync(filePath); const mimeType = 'image/' + path.extname(filePath).substring(1); const base64Data = fileData.toString('base64'); return `data:${mimeType};base64,${base64Data}`; } catch (error) { console.error("Erreur de lecture du fichier image:", error); return null; } });
ipcMain.handle('theme:get', () => handleRequest(() => db.settingsDB.get('theme'), 'theme:get'));
ipcMain.handle('theme:set', (event, theme) => handleRequest(() => { nativeTheme.themeSource = theme; db.settingsDB.save('theme', theme); return nativeTheme.shouldUseDarkColors; }, 'theme:set'));

// --- Canaux pour l'internationalisation (i18n) ---
ipcMain.handle('settings:get-language', () => handleRequest(() => db.settingsDB.get('language'), 'settings:get-language'));
ipcMain.handle('settings:set-language', (event, lang) => handleRequest(() => { db.settingsDB.save('language', lang); return { success: true }; }, 'settings:set-language'));
ipcMain.handle('app:reload', () => { if (mainWindow) { mainWindow.reload(); } });

ipcMain.handle('i18n:get-translation', (event, lang) => {
    // Valider le code de langue pour la sécurité
    if (!lang || !/^[a-z]{2}$/.test(lang)) {
        console.error('Tentative de chargement avec un code de langue invalide:', lang);
        throw new Error('Code de langue invalide.');
    }
    const filePath = path.join(__dirname, 'src', 'locales', `${lang}.json`);
    try {
        const data = fs.readFileSync(filePath, 'utf8');
        return JSON.parse(data);
    } catch (error) {
        console.error(`Impossible de lire le fichier de traduction : ${filePath}`, error);
        return {}; // Renvoyer un objet vide pour éviter un crash
    }
});

// --- Autres canaux IPC ---
ipcMain.handle('products:get-all', (event, searchTerm) => handleRequest(() => db.productDB.getAll(searchTerm), 'products:get-all'));
ipcMain.handle('products:get-by-id', (event, id) => handleRequest(() => db.productDB.getById(id), 'products:get-by-id'));
ipcMain.handle('products:get-categories', () => handleRequest(() => db.productDB.getCategories(), 'products:get-categories'));
ipcMain.handle('products:get-low-stock', () => handleRequest(() => db.productDB.getLowStock(), 'products:get-low-stock'));
ipcMain.handle('products:add', (event, payload) => handleRequest(() => db.productDB.add(payload), 'products:add'));
ipcMain.handle('products:update', (event, payload) => handleRequest(() => db.productDB.update(payload), 'products:update'));
ipcMain.handle('products:delete', (event, payload) => handleRequest(() => db.productDB.delete(payload), 'products:delete'));
ipcMain.handle('clients:get-all', (event, searchTerm) => handleRequest(() => db.clientDB.getAll(searchTerm), 'clients:get-all'));
ipcMain.handle('clients:get-by-id', (event, id) => handleRequest(() => db.clientDB.getById(id), 'clients:get-by-id'));
ipcMain.handle('clients:add', (event, payload) => handleRequest(() => db.clientDB.add(payload), 'clients:add'));
ipcMain.handle('clients:update', (event, payload) => handleRequest(() => db.clientDB.update(payload), 'clients:update'));
ipcMain.handle('clients:delete', (event, payload) => handleRequest(() => db.clientDB.delete(payload), 'clients:delete'));
ipcMain.handle('sales:get-history', (event, filters) => handleRequest(() => db.salesDB.getHistory(filters), 'sales:get-history'));
ipcMain.handle('sales:get-details', (event, saleId) => handleRequest(() => db.salesDB.getDetails(saleId), 'sales:get-details'));
ipcMain.handle('invoices:get-all', () => handleRequest(() => db.invoicesDB.getAll(), 'invoices:get-all'));
ipcMain.handle('invoices:get-details', (event, id) => handleRequest(() => db.invoicesDB.getDetails(id), 'invoices:get-details'));
ipcMain.handle('invoices:get-next-number', () => handleRequest(() => db.invoicesDB.getNextNumber(), 'invoices:get-next-number'));
ipcMain.handle('invoices:create', (event, payload) => handleRequest(() => db.invoicesDB.create(payload), 'invoices:create'));
ipcMain.handle('settings:get-company-info', () => handleRequest(() => db.settingsDB.getCompanyInfo(), 'settings:get-company-info'));
ipcMain.handle('settings:save-company-info', (event, payload) => handleRequest(() => db.settingsDB.saveCompanyInfo(payload), 'settings:save-company-info'));
ipcMain.handle('users:get-all', () => handleRequest(() => db.usersDB.getAll(), 'users:get-all'));
ipcMain.handle('users:add', (event, payload) => handleRequest(() => db.usersDB.add(payload.username, payload.password), 'users:add'));
ipcMain.handle('users:update-password', (event, payload) => handleRequest(() => db.usersDB.updateUserPassword(payload.id, payload.password), 'users:update-password'));
ipcMain.handle('users:delete', (event, payload) => handleRequest(() => db.usersDB.deleteUser(payload), 'users:delete'));
ipcMain.handle('dashboard:get-stats', (event, range) => handleRequest(() => db.dashboardDB.getStats(range), 'dashboard:get-stats'));
ipcMain.handle('dashboard:get-top-profitable', (event, params) => handleRequest(() => db.dashboardDB.getTopProfitable(params), 'dashboard:get-top-profitable'));
ipcMain.handle('dashboard:get-top-selling', (event, params) => handleRequest(() => db.dashboardDB.getTopSelling(params), 'dashboard:get-top-selling'));
ipcMain.handle('dashboard:get-performance-overview', (event, params) => handleRequest(() => db.dashboardDB.getPerformanceOverview(params), 'dashboard:get-performance-overview'));
ipcMain.handle('dashboard:get-insights', (event, params) => handleRequest(() => db.dashboardDB.getInsights(params), 'dashboard:get-insights'));
ipcMain.handle('credits:get-debtors', () => handleRequest(() => db.creditsDB.getDebtors(), 'credits:get-debtors'));
ipcMain.handle('credits:get-client-credit', (event, clientId) => handleRequest(() => db.creditsDB.getClientCredit(clientId), 'credits:get-client-credit'));
ipcMain.handle('print:to-pdf', async (event, htmlContent) => { let tempWin; try { tempWin = new BrowserWindow({ show: false, parent: mainWindow }); await tempWin.loadURL(`data:text/html;charset=utf-8,${encodeURIComponent(htmlContent)}`); const pdf = await tempWin.webContents.printToPDF({ marginsType: 0, pageSize: 'A4', printBackground: true }); return pdf; } finally { if (tempWin) tempWin.close(); } });

// API pour générer et sauvegarder des tickets PDF
ipcMain.handle('pdf:generate-ticket', async (event, htmlContent, filename) => {
    return handleRequest(async () => {
        let tempWin;
        try {
            // Créer une fenêtre temporaire pour le rendu
            tempWin = new BrowserWindow({
                show: false,
                parent: mainWindow,
                webPreferences: {
                    nodeIntegration: false,
                    contextIsolation: true
                }
            });

            // Charger le contenu HTML
            await tempWin.loadURL(`data:text/html;charset=utf-8,${encodeURIComponent(htmlContent)}`);

            // Générer le PDF avec des options optimisées pour les tickets
            const pdf = await tempWin.webContents.printToPDF({
                marginsType: 0,
                pageSize: {
                    width: 80000, // 80mm en microns
                    height: 200000 // Hauteur variable
                },
                printBackground: true,
                landscape: false
            });

            // Demander à l'utilisateur où sauvegarder le fichier
            const { canceled, filePath } = await dialog.showSaveDialog(mainWindow, {
                title: 'Sauvegarder le ticket',
                defaultPath: filename || 'ticket.pdf',
                filters: [
                    { name: 'PDF Files', extensions: ['pdf'] }
                ]
            });

            if (!canceled && filePath) {
                // Sauvegarder le fichier
                fs.writeFileSync(filePath, pdf);
                return { success: true, filePath };
            } else {
                return { success: false, message: 'Sauvegarde annulée' };
            }

        } catch (error) {
            console.error('Erreur lors de la génération du PDF:', error);
            throw error;
        } finally {
            if (tempWin) tempWin.close();
        }
    }, 'pdf:generate-ticket');
});

// --- Canaux nécessitant un utilisateur connecté ---
ipcMain.handle('users:update-credentials', (event, payload) => handleUserRequest(async () => { const { newUsername, currentPassword, newPassword } = payload; await db.usersDB.updateCredentials({ id: currentUser.id, currentUsername: currentUser.username, newUsername, currentPassword, newPassword }); if (currentUser.username !== newUsername) { currentUser.username = newUsername; } return { success: true, message: "Vos informations ont été mises à jour avec succès." }; }, 'users:update-credentials'));
ipcMain.handle('sales:get-last', (event) => handleUserRequest(() => db.salesDB.getLast(currentUser.id), 'sales:get-last'));
ipcMain.handle('sales:process', (event, payload) => handleUserRequest(() => db.salesDB.process({ ...payload, userId: currentUser.id }), 'sales:process'));
ipcMain.handle('sales:edit', (event, payload) => handleUserRequest(() => db.salesDB.edit(payload.originalSaleId, { ...payload.newSaleData, userId: currentUser.id }), 'sales:edit'));
ipcMain.handle('sales:get-history-for-user', () => handleUserRequest(() => db.salesDB.getHistoryForUser(currentUser.id), 'sales:get-history-for-user'));
ipcMain.handle('products:adjust-stock', (event, payload) => handleUserRequest(() => db.productDB.adjustStock(payload.adjustments, payload.reason, currentUser.id), 'products:adjust-stock'));
ipcMain.handle('products:update-threshold', (event, productId, threshold) => handleUserRequest(() => db.productDB.updateThreshold(productId, threshold), 'products:update-threshold'));
ipcMain.handle('credits:record-payment', (event, payload) => handleUserRequest(() => db.creditsDB.recordPayment({ ...payload, userId: currentUser.id }), 'credits:record-payment'));
ipcMain.handle('credits:add-manual', (event, payload) => handleUserRequest(() => db.creditsDB.addManual({ ...payload, userId: currentUser.id }), 'credits:add-manual'));
ipcMain.handle('sales:process-return', (event, payload) => handleUserRequest(() => db.salesDB.processReturn(payload.originalSaleId, payload.itemsToReturn, payload.clientId), 'sales:process-return'));