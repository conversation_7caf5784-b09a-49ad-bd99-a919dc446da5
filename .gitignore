# Dépendances Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Base de données (IMPORTANT : Ne pas partager la vraie DB)
*.db
*.sqlite
*.sqlite3
database.db
gestion.db

# Fichiers de configuration sensibles
.env
.env.local
.env.production
config/database.json

# Fichiers temporaires
*.tmp
*.temp
*.log

# Fichiers système
.DS_Store
Thumbs.db
desktop.ini

# Dossiers de build
dist/
build/
out/

# Fichiers d'éditeur
.vscode/
.idea/
*.swp
*.swo
*~

# Electron
app/
release/

# Fichiers de sauvegarde
*.bak
*.backup

# Fichiers de cache
.cache/
.parcel-cache/

# Fichiers de test
coverage/
.nyc_output/

# Logs d'application
logs/
*.log

# Fichiers de développement local
.local/
.dev/
