<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="script-src 'self'">
    <title data-i18n="settings_page_title">Paramètres - Système de Gestion</title>
    <link href="./css/output.css" rel="stylesheet">
    <style>
        /* Styles personnalisés pour la page Paramètres */
        .settings-card {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            border: 1px solid #e2e8f0;
            transition: all 0.3s ease;
        }
        .dark .settings-card {
            background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
            border: 1px solid #374151;
        }
        .settings-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        .dark .settings-card:hover {
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
        }
        .settings-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .form-input {
            transition: all 0.2s ease;
            border: 2px solid #e2e8f0;
        }
        .form-input:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            transform: translateY(-1px);
        }
        .dark .form-input {
            border-color: #374151;
            background-color: #1f2937;
        }
        .dark .form-input:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
        }
        .theme-btn {
            transition: all 0.2s ease;
            position: relative;
            overflow: hidden;
        }
        .theme-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }
        .theme-btn:hover::before {
            left: 100%;
        }
        .section-icon {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .save-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            transition: all 0.3s ease;
        }
        .save-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }
        .add-btn {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            transition: all 0.3s ease;
        }
        .add-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(16, 185, 129, 0.3);
        }
        .danger-btn {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            transition: all 0.3s ease;
        }
        .danger-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(239, 68, 68, 0.3);
        }
        .settings-table {
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        .table-header {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        }
        .dark .table-header {
            background: linear-gradient(135deg, #374151 0%, #1f2937 100%);
        }
        .table-row {
            transition: all 0.2s ease;
        }
        .table-row:hover {
            background-color: #f8fafc;
            transform: translateX(4px);
        }
        .dark .table-row:hover {
            background-color: #374151;
        }
    </style>
</head>
<body class="bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 flex h-screen">
    <!-- La sidebar sera créée dynamiquement par sidebar-manager.js -->

    <main class="flex-1 p-8 overflow-y-auto">
        <!-- En-tête de la page avec design moderne -->
        <div class="mb-8">
            <div class="flex items-center gap-4 mb-4">
                <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                </div>
                <div>
                    <h1 class="text-4xl font-bold settings-header" data-i18n="settings_page_title">Paramètres</h1>
                    <p class="text-gray-600 dark:text-gray-400 mt-1">Configurez votre application selon vos préférences</p>
                </div>
            </div>

            <!-- Barre de navigation des sections -->
            <div class="flex flex-wrap gap-2 p-1 bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
                <button class="section-nav-btn active px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200" data-section="appearance">
                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z"></path>
                    </svg>
                    Apparence
                </button>
                <button class="section-nav-btn px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200" data-section="company">
                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h4M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                    </svg>
                    Société
                </button>
                <button class="section-nav-btn px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200" data-section="security">
                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                    </svg>
                    Sécurité
                </button>
                <button class="section-nav-btn px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200" data-section="users">
                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                    </svg>
                    Utilisateurs
                </button>
            </div>
        </div>
        
        <div class="space-y-8">
            <!-- Section Apparence -->
            <div id="appearance-section" class="settings-card p-8 rounded-2xl shadow-lg">
                <div class="flex items-center gap-3 mb-6">
                    <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z"></path>
                        </svg>
                    </div>
                    <div>
                        <h2 class="text-2xl font-bold text-gray-800 dark:text-white" data-i18n="appearance_section_title">Apparence</h2>
                        <p class="text-gray-600 dark:text-gray-400 text-sm">Personnalisez l'apparence de votre interface</p>
                    </div>
                </div>

                <div class="space-y-8">
                    <!-- Sélection du thème -->
                    <div class="bg-gray-50 dark:bg-gray-800/50 p-6 rounded-xl">
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center gap-3">
                                <svg class="w-5 h-5 section-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
                                </svg>
                                <div>
                                    <span class="font-semibold text-gray-800 dark:text-white" data-i18n="app_theme_label">Thème de l'application</span>
                                    <p class="text-sm text-gray-600 dark:text-gray-400">Choisissez votre thème préféré</p>
                                </div>
                            </div>
                        </div>
                        <div class="grid grid-cols-3 gap-4">
                            <button data-theme="light" class="theme-btn group p-4 rounded-xl border-2 border-gray-200 dark:border-gray-600 hover:border-blue-500 transition-all duration-200">
                                <div class="w-8 h-8 bg-white rounded-lg shadow-sm mx-auto mb-2 border border-gray-200"></div>
                                <span class="text-sm font-medium" data-i18n="theme_light">Clair</span>
                            </button>
                            <button data-theme="dark" class="theme-btn group p-4 rounded-xl border-2 border-gray-200 dark:border-gray-600 hover:border-blue-500 transition-all duration-200">
                                <div class="w-8 h-8 bg-gray-800 rounded-lg shadow-sm mx-auto mb-2"></div>
                                <span class="text-sm font-medium" data-i18n="theme_dark">Sombre</span>
                            </button>
                            <button data-theme="system" class="theme-btn group p-4 rounded-xl border-2 border-gray-200 dark:border-gray-600 hover:border-blue-500 transition-all duration-200">
                                <div class="w-8 h-8 bg-gradient-to-br from-white to-gray-800 rounded-lg shadow-sm mx-auto mb-2"></div>
                                <span class="text-sm font-medium" data-i18n="theme_system">Système</span>
                            </button>
                        </div>
                    </div>

                    <!-- Sélection de la langue -->
                    <div class="bg-gray-50 dark:bg-gray-800/50 p-6 rounded-xl">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-3">
                                <svg class="w-5 h-5 section-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129"></path>
                                </svg>
                                <div>
                                    <span class="font-semibold text-gray-800 dark:text-white" data-i18n="app_language_label">Langue de l'application</span>
                                    <p class="text-sm text-gray-600 dark:text-gray-400">Sélectionnez votre langue préférée</p>
                                </div>
                            </div>
                            <div class="relative">
                                <select id="language-selector" class="form-input appearance-none bg-white dark:bg-gray-700 border-2 border-gray-200 dark:border-gray-600 rounded-xl px-4 py-3 pr-10 text-sm font-medium focus:outline-none">
                                    <option value="fr">🇫🇷 Français</option>
                                    <option value="ar">🇲🇦 العربية</option>
                                </select>
                                <svg class="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Section Informations de la Société -->
            <div id="company-section" class="settings-card p-8 rounded-2xl shadow-lg">
                <div class="flex items-center gap-3 mb-6">
                    <div class="w-10 h-10 bg-gradient-to-br from-green-500 to-teal-600 rounded-xl flex items-center justify-center">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h4M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                        </svg>
                    </div>
                    <div>
                        <h2 class="text-2xl font-bold text-gray-800 dark:text-white" data-i18n="company_info_section_title">Informations de la Société</h2>
                        <p class="text-gray-600 dark:text-gray-400 text-sm">Configurez les informations de votre entreprise</p>
                    </div>
                </div>

                <form id="companyInfoForm" class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Nom de la société -->
                        <div class="md:col-span-2">
                            <label for="company_name" class="flex items-center gap-2 text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2" data-i18n="company_name_label">
                                <svg class="w-4 h-4 section-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h4M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                </svg>
                                Nom de la société
                            </label>
                            <input type="text" id="company_name" name="company_name" class="form-input w-full px-4 py-3 rounded-xl text-sm" placeholder="Entrez le nom de votre société">
                        </div>

                        <!-- Téléphone -->
                        <div>
                            <label for="company_phone" class="flex items-center gap-2 text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2" data-i18n="client_phone_label">
                                <svg class="w-4 h-4 section-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                </svg>
                                Téléphone
                            </label>
                            <input type="text" id="company_phone" name="company_phone" class="form-input w-full px-4 py-3 rounded-xl text-sm" placeholder="+212 6XX XXX XXX">
                        </div>

                        <!-- Email -->
                        <div>
                            <label for="company_email" class="flex items-center gap-2 text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2" data-i18n="company_email_label">
                                <svg class="w-4 h-4 section-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                </svg>
                                Email
                            </label>
                            <input type="email" id="company_email" name="company_email" class="form-input w-full px-4 py-3 rounded-xl text-sm" placeholder="<EMAIL>">
                        </div>

                        <!-- ICE -->
                        <div>
                            <label for="company_ice" class="flex items-center gap-2 text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2" data-i18n="company_ice_label">
                                <svg class="w-4 h-4 section-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                ICE
                            </label>
                            <input type="text" id="company_ice" name="company_ice" class="form-input w-full px-4 py-3 rounded-xl text-sm" placeholder="000000000000000">
                        </div>

                        <!-- Site Web -->
                        <div>
                            <label for="company_website" class="flex items-center gap-2 text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2" data-i18n="company_website_label">
                                <svg class="w-4 h-4 section-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9"></path>
                                </svg>
                                Site Web
                            </label>
                            <input type="text" id="company_website" name="company_website" class="form-input w-full px-4 py-3 rounded-xl text-sm" placeholder="https://www.entreprise.com">
                        </div>

                        <!-- Adresse -->
                        <div class="md:col-span-2">
                            <label for="company_address" class="flex items-center gap-2 text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2" data-i18n="client_address_label">
                                <svg class="w-4 h-4 section-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                                Adresse
                            </label>
                            <textarea id="company_address" name="company_address" rows="3" class="form-input w-full px-4 py-3 rounded-xl text-sm resize-none" placeholder="Adresse complète de votre société"></textarea>
                        </div>
                    </div>

                    <div class="flex justify-end pt-4 border-t border-gray-200 dark:border-gray-700">
                        <button type="submit" class="save-btn text-white px-8 py-3 rounded-xl font-semibold flex items-center gap-2" data-i18n="save_info_button">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            Sauvegarder les informations
                        </button>
                    </div>
                </form>
            </div>

            <!-- Section Sécurité -->
            <div id="security-section" class="settings-card p-8 rounded-2xl shadow-lg hidden">
                <div class="flex items-center gap-3 mb-6">
                    <div class="w-10 h-10 bg-gradient-to-br from-red-500 to-pink-600 rounded-xl flex items-center justify-center">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                        </svg>
                    </div>
                    <div>
                        <h2 class="text-2xl font-bold text-gray-800 dark:text-white" data-i18n="owner_account_security_title">Sécurité du Compte Propriétaire</h2>
                        <p class="text-gray-600 dark:text-gray-400 text-sm">Gérez vos informations de connexion et sécurité</p>
                    </div>
                </div>

                <!-- Alerte de sécurité -->
                <div class="bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-xl p-4 mb-6">
                    <div class="flex items-start gap-3">
                        <svg class="w-5 h-5 text-amber-600 dark:text-amber-400 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.268 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                        <div>
                            <h4 class="font-semibold text-amber-800 dark:text-amber-200">Attention</h4>
                            <p class="text-sm text-amber-700 dark:text-amber-300">Assurez-vous d'utiliser un mot de passe fort et unique pour protéger votre compte.</p>
                        </div>
                    </div>
                </div>

                <form id="ownerCredentialsForm" class="space-y-6">
                    <!-- Nom d'utilisateur -->
                    <div class="bg-gray-50 dark:bg-gray-800/50 p-6 rounded-xl">
                        <h3 class="font-semibold text-gray-800 dark:text-white mb-4 flex items-center gap-2">
                            <svg class="w-4 h-4 section-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                            Informations du compte
                        </h3>
                        <div>
                            <label for="ownerUsername" class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2" data-i18n="username_label">Nom d'utilisateur</label>
                            <input type="text" id="ownerUsername" name="ownerUsername" class="form-input w-full px-4 py-3 rounded-xl text-sm" placeholder="Votre nom d'utilisateur" required>
                        </div>
                    </div>

                    <!-- Mot de passe actuel -->
                    <div class="bg-gray-50 dark:bg-gray-800/50 p-6 rounded-xl">
                        <h3 class="font-semibold text-gray-800 dark:text-white mb-4 flex items-center gap-2">
                            <svg class="w-4 h-4 section-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                            </svg>
                            Vérification d'identité
                        </h3>
                        <div>
                            <label for="currentPassword" class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2" data-i18n="current_password_label">Mot de passe actuel</label>
                            <input type="password" id="currentPassword" name="currentPassword" class="form-input w-full px-4 py-3 rounded-xl text-sm" placeholder="Entrez votre mot de passe actuel" required>
                        </div>
                    </div>

                    <!-- Nouveau mot de passe -->
                    <div class="bg-gray-50 dark:bg-gray-800/50 p-6 rounded-xl">
                        <h3 class="font-semibold text-gray-800 dark:text-white mb-4 flex items-center gap-2">
                            <svg class="w-4 h-4 section-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                            </svg>
                            Nouveau mot de passe (optionnel)
                        </h3>
                        <div class="space-y-4">
                            <div>
                                <label for="newPassword" class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2" data-i18n="new_password_label">Nouveau mot de passe</label>
                                <input type="password" id="newPassword" name="newPassword" class="form-input w-full px-4 py-3 rounded-xl text-sm" placeholder="Laissez vide pour conserver le mot de passe actuel">
                            </div>
                            <div>
                                <label for="confirmNewPassword" class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2" data-i18n="confirm_new_password_label">Confirmer le nouveau mot de passe</label>
                                <input type="password" id="confirmNewPassword" name="confirmNewPassword" class="form-input w-full px-4 py-3 rounded-xl text-sm" placeholder="Confirmez votre nouveau mot de passe">
                            </div>
                        </div>

                        <!-- Indicateur de force du mot de passe -->
                        <div class="mt-4">
                            <div class="text-xs text-gray-600 dark:text-gray-400 mb-2">Force du mot de passe:</div>
                            <div class="flex gap-1">
                                <div class="h-2 w-full bg-gray-200 dark:bg-gray-700 rounded-full">
                                    <div id="password-strength" class="h-2 bg-red-400 rounded-full transition-all duration-300" style="width: 0%"></div>
                                </div>
                            </div>
                            <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                Utilisez au moins 8 caractères avec des lettres, chiffres et symboles
                            </div>
                        </div>
                    </div>

                    <div class="flex justify-end pt-4 border-t border-gray-200 dark:border-gray-700">
                        <button type="submit" class="save-btn text-white px-8 py-3 rounded-xl font-semibold flex items-center gap-2" data-i18n="update_button">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                            </svg>
                            Mettre à jour
                        </button>
                    </div>
                </form>
            </div>

            <!-- Section Gestion des Utilisateurs -->
            <div id="users-section" class="settings-card p-8 rounded-2xl shadow-lg hidden">
                <div class="flex items-center gap-3 mb-6">
                    <div class="w-10 h-10 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                        </svg>
                    </div>
                    <div>
                        <h2 class="text-2xl font-bold text-gray-800 dark:text-white" data-i18n="sellers_management_title">Gestion des Vendeurs</h2>
                        <p class="text-gray-600 dark:text-gray-400 text-sm">Ajoutez et gérez les comptes vendeurs</p>
                    </div>
                </div>

                <!-- Formulaire d'ajout d'utilisateur -->
                <div class="bg-gray-50 dark:bg-gray-800/50 p-6 rounded-xl mb-8">
                    <h3 class="font-semibold text-gray-800 dark:text-white mb-4 flex items-center gap-2">
                        <svg class="w-4 h-4 section-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Ajouter un nouveau vendeur
                    </h3>

                    <form id="addUserForm" class="grid grid-cols-1 md:grid-cols-3 gap-4 items-end">
                        <div>
                            <label for="username" class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2" data-i18n="username_label">Nom d'utilisateur</label>
                            <input type="text" id="username" class="form-input w-full px-4 py-3 rounded-xl text-sm" placeholder="Nom d'utilisateur unique" required>
                        </div>
                        <div>
                            <label for="password" class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2" data-i18n="password_label">Mot de passe</label>
                            <input type="password" id="password" class="form-input w-full px-4 py-3 rounded-xl text-sm" placeholder="Mot de passe sécurisé" required>
                        </div>
                        <button type="submit" class="add-btn text-white px-6 py-3 rounded-xl font-semibold flex items-center justify-center gap-2" data-i18n="add_seller_button">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            Ajouter Vendeur
                        </button>
                    </form>
                </div>

                <!-- Liste des utilisateurs -->
                <div class="bg-gray-50 dark:bg-gray-800/50 p-6 rounded-xl">
                    <h3 class="font-semibold text-gray-800 dark:text-white mb-4 flex items-center gap-2">
                        <svg class="w-4 h-4 section-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                        Vendeurs existants
                    </h3>

                    <div class="overflow-hidden rounded-xl border border-gray-200 dark:border-gray-700">
                        <table class="settings-table w-full">
                            <thead class="table-header">
                                <tr>
                                    <th class="px-6 py-4 text-left text-sm font-semibold text-gray-700 dark:text-gray-300" data-i18n="username_header">
                                        <div class="flex items-center gap-2">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                            </svg>
                                            Nom d'utilisateur
                                        </div>
                                    </th>
                                    <th class="px-6 py-4 text-left text-sm font-semibold text-gray-700 dark:text-gray-300">
                                        <div class="flex items-center gap-2">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                            </svg>
                                            Statut
                                        </div>
                                    </th>
                                    <th class="px-6 py-4 text-right text-sm font-semibold text-gray-700 dark:text-gray-300" data-i18n="actions_header">
                                        <div class="flex items-center justify-end gap-2">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"></path>
                                            </svg>
                                            Actions
                                        </div>
                                    </th>
                                </tr>
                            </thead>
                            <tbody id="usersTableBody" class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                <!-- Les utilisateurs seront ajoutés dynamiquement ici -->
                            </tbody>
                        </table>
                    </div>

                    <!-- Message si aucun utilisateur -->
                    <div id="no-users-message" class="text-center py-8 text-gray-500 dark:text-gray-400 hidden">
                        <svg class="w-12 h-12 mx-auto mb-4 text-gray-300 dark:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                        <p class="text-lg font-medium">Aucun vendeur ajouté</p>
                        <p class="text-sm">Commencez par ajouter votre premier vendeur ci-dessus</p>
                    </div>
                </div>
            </div>
        </div>
    </main>
    
    <div id="confirmationModal"></div>

    <script src="./js/i18n.js"></script>
    <script src="./js/preloader.js"></script>
    <script src="./js/sidebar-manager.js"></script>
    <script src="./js/page-initializer.js"></script>
    <script src="./js/notifications.js"></script>
    <script src="./js/layout.js"></script>
    <script src="./js/settings.js"></script>
</body>
</html>